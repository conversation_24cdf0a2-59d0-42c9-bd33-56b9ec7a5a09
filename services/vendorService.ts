/**
 * Servicio para la gestión de vendedores (usuarios con rol ROLE_VENDEDOR)
 */

import { 
  UserDetailResponse, 
  CreateVendedorRequest, 
  MessageResponse,
  ApiError 
} from '../types/auth';
import { getApiConfig, getAuthHeaders, buildUrl } from '../config/api';

class VendorService {
  private apiConfig = getApiConfig();

  /**
   * Realiza una petición HTTP con manejo de errores
   */
  private async makeRequest<T>(
    url: string, 
    options: RequestInit = {},
    token: string
  ): Promise<T> {
    try {
      // Agregar modo 'cors' explícitamente
      const response = await fetch(url, {
        ...options,
        mode: 'cors',
        headers: {
          ...getAuthHeaders(token),
          ...options.headers,
        },
      });

      // Verificar si hay respuesta
      if (!response) {
        throw {
          message: 'No se recibió respuesta del servidor',
          status: 0,
        } as ApiError;
      }

      // Intentar parsear la respuesta como JSON
      let data;
      try {
        data = await response.json();
      } catch (e) {
        // Si no es JSON, usar un objeto vacío
        data = {};
      }

      if (!response.ok) {
        // Manejar errores HTTP
        if (response.status === 401) {
          throw {
            message: 'Sesión expirada o no autorizada. Por favor, inicie sesión nuevamente.',
            status: 401,
            code: 'UNAUTHORIZED',
          } as ApiError;
        } else if (response.status === 403) {
          throw {
            message: 'No tiene permisos para acceder a este recurso.',
            status: 403,
            code: 'FORBIDDEN',
          } as ApiError;
        } else {
          throw {
            message: data.message || `Error en la petición (${response.status})`,
            status: response.status,
            code: data.code,
          } as ApiError;
        }
      }

      return data;
    } catch (error: any) {
      // Verificar si es un error de CORS
      if (error.message && (
          error.message.includes('NetworkError') || 
          error.message.includes('Network request failed') ||
          error.message.includes('CORS')
        )) {
        console.error('Error CORS detectado:', error);
        throw {
          message: 'Error de conexión con el servidor. Posible problema de CORS.',
          status: 0,
          code: 'CORS_ERROR',
        } as ApiError;
      }
      
      // Error de red general
      if (error instanceof TypeError) {
        console.error('Error de red:', error);
        throw {
          message: 'Error de conexión. Verifica tu conexión a internet.',
          status: 0,
          code: 'NETWORK_ERROR',
        } as ApiError;
      }
      
      // Reenviar el error original si ya es un ApiError
      throw error;
    }
  }

  /**
   * Obtiene la lista de todos los vendedores
   */
  async getVendors(token: string): Promise<UserDetailResponse[]> {
    console.log('🔄 Obteniendo lista de vendedores...');
    console.log('🔑 Token disponible:', !!token);
    
    const url = buildUrl(this.apiConfig.ENDPOINTS.ADMIN.VENDORS.LIST);
    console.log('📡 URL de la petición:', url);
    
    try {
      const data = await this.makeRequest<UserDetailResponse[]>(url, {
        method: 'GET',
      }, token);
      
      console.log('✅ Vendedores obtenidos:', data.length);
      return data;
    } catch (error) {
      console.error('❌ Error al obtener vendedores:', error);
      throw error;
    }
  }

  /**
   * Obtiene los detalles de un vendedor específico
   */
  async getVendorById(id: number, token: string): Promise<UserDetailResponse> {
    console.log(`🔄 Obteniendo detalles del vendedor ID: ${id}...`);
    
    const url = buildUrl(`${this.apiConfig.ENDPOINTS.ADMIN.VENDORS.DETAIL}/${id}`);
    console.log('📡 URL de la petición:', url);
    
    try {
      const data = await this.makeRequest<UserDetailResponse>(url, {
        method: 'GET',
      }, token);
      
      console.log('✅ Detalles del vendedor obtenidos');
      return data;
    } catch (error) {
      console.error('❌ Error al obtener detalles del vendedor:', error);
      throw error;
    }
  }

  /**
   * Crea un nuevo vendedor
   */
  async createVendor(vendorData: CreateVendedorRequest, token: string): Promise<UserDetailResponse> {
    console.log('🔄 Creando nuevo vendedor...');
    
    const url = buildUrl(this.apiConfig.ENDPOINTS.ADMIN.VENDORS.LIST);
    console.log('📡 URL de la petición:', url);
    console.log('📦 Datos enviados:', vendorData);
    
    try {
      const data = await this.makeRequest<UserDetailResponse>(url, {
        method: 'POST',
        body: JSON.stringify(vendorData),
      }, token);
      
      console.log('✅ Vendedor creado con éxito');
      return data;
    } catch (error) {
      console.error('❌ Error al crear vendedor:', error);
      throw error;
    }
  }

  /**
   * Activa o desactiva un vendedor
   */
  async setVendorStatus(id: number, active: boolean, token: string): Promise<MessageResponse> {
    console.log(`🔄 Cambiando estado del vendedor ID: ${id} a ${active ? 'activo' : 'inactivo'}...`);
    
    const url = buildUrl(`${this.apiConfig.ENDPOINTS.ADMIN.VENDORS.STATUS}/${id}/active?active=${active}`);
    console.log('📡 URL de la petición:', url);
    
    try {
      const data = await this.makeRequest<MessageResponse>(url, {
        method: 'PATCH',
      }, token);
      
      console.log('✅ Estado del vendedor actualizado');
      return data;
    } catch (error) {
      console.error('❌ Error al cambiar estado del vendedor:', error);
      throw error;
    }
  }
}

// Exportar instancia singleton
export const vendorService = new VendorService();
export default vendorService;