/**
 * Servicio de autenticación para consumir la API de ecommerce
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  LoginRequest, 
  SignupRequest, 
  JwtResponse, 
  MessageResponse, 
  SessionInfoResponse,
  ApiError 
} from '../types/auth';
import { getApiConfig, getAuthHeaders, buildUrl } from '../config/api';

// Constantes para AsyncStorage
const STORAGE_KEYS = {
  TOKEN: '@commerce_auth_token',
  USER: '@commerce_auth_user',
};

class AuthService {
  private apiConfig = getApiConfig();

  /**
   * Realiza una petición HTTP con manejo de errores
   */
  private async makeRequest<T>(
    url: string, 
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw {
          message: data.message || 'Error en la petición',
          status: response.status,
          code: data.code,
        } as ApiError;
      }

      return data;
    } catch (error) {
      if (error instanceof TypeError) {
        // Error de red
        throw {
          message: 'Error de conexión. Verifica tu conexión a internet.',
          status: 0,
        } as ApiError;
      }
      throw error;
    }
  }

  /**
   * Inicia sesión con username y password
   */
  async login(credentials: LoginRequest): Promise<JwtResponse> {
    const url = buildUrl(this.apiConfig.ENDPOINTS.AUTH.LOGIN);
    
    const response = await this.makeRequest<JwtResponse>(url, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(credentials),
    });

    // Guardar token en AsyncStorage
    await this.saveToken(response.token);
    await this.saveUser({
      id: response.id,
      username: response.username,
      email: response.email,
      roles: response.roles,
    });

    return response;
  }

  /**
   * Registra un nuevo usuario
   */
  async signup(userData: SignupRequest): Promise<MessageResponse> {
    const url = buildUrl(this.apiConfig.ENDPOINTS.AUTH.SIGNUP);
    
    return await this.makeRequest<MessageResponse>(url, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData),
    });
  }

  /**
   * Obtiene información de la sesión actual
   */
  async getSessionInfo(): Promise<SessionInfoResponse> {
    const token = await this.getToken();
    
    if (!token) {
      throw {
        message: 'No hay token de autenticación',
        status: 401,
      } as ApiError;
    }

    const url = buildUrl(this.apiConfig.ENDPOINTS.AUTH.SESSION_INFO);
    
    return await this.makeRequest<SessionInfoResponse>(url, {
      method: 'GET',
      headers: getAuthHeaders(token),
    });
  }

  /**
   * Cierra la sesión del usuario
   */
  async logout(): Promise<MessageResponse> {
    console.log('🔄 AuthService: Iniciando logout...');
    const token = await this.getToken();
    console.log('🔑 Token encontrado:', !!token);

    if (token) {
      try {
        const url = buildUrl(this.apiConfig.ENDPOINTS.AUTH.LOGOUT);
        console.log('📡 Enviando petición de logout al servidor:', url);

        await this.makeRequest<MessageResponse>(url, {
          method: 'POST',
          headers: getAuthHeaders(token),
        });
        console.log('✅ Logout en servidor exitoso');
      } catch (error) {
        // Incluso si falla la petición al servidor, limpiamos el storage local
        console.warn('❌ Error al cerrar sesión en el servidor:', error);
      }
    }

    // Limpiar storage local
    console.log('🧹 Limpiando datos locales...');
    await this.clearAuthData();
    console.log('✅ Datos locales limpiados');

    return { message: 'Sesión cerrada exitosamente' };
  }

  /**
   * Guarda el token en AsyncStorage
   */
  async saveToken(token: string): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, token);
  }

  /**
   * Obtiene el token de AsyncStorage
   */
  async getToken(): Promise<string | null> {
    return await AsyncStorage.getItem(STORAGE_KEYS.TOKEN);
  }

  /**
   * Guarda los datos del usuario en AsyncStorage
   */
  async saveUser(user: any): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
  }

  /**
   * Obtiene los datos del usuario de AsyncStorage
   */
  async getUser(): Promise<any | null> {
    const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER);
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * Limpia todos los datos de autenticación
   */
  async clearAuthData(): Promise<void> {
    console.log('🧹 Eliminando token y datos de usuario del storage...');
    await AsyncStorage.multiRemove([STORAGE_KEYS.TOKEN, STORAGE_KEYS.USER]);
    console.log('✅ Storage limpiado completamente');
  }

  /**
   * Verifica si hay una sesión activa
   */
  async hasActiveSession(): Promise<boolean> {
    const token = await this.getToken();
    return !!token;
  }
}

// Exportar instancia singleton
export const authService = new AuthService();
export default authService;
