/**
 * Context de autenticación para React Native
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { router } from 'expo-router';
import {
  AuthContextType,
  AuthState,
  LoginRequest,
  SignupRequest,
  User,
  ApiError
} from '../types/auth';
import authService from '../services/authService';

// Estado inicial
const initialState: AuthState = {
  user: null,
  token: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
};

// Tipos de acciones
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_TOKEN'; payload: string | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' }
  | { type: 'LOGOUT' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } };

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_USER':
      return { 
        ...state, 
        user: action.payload,
        isAuthenticated: !!action.payload,
      };
    
    case 'SET_TOKEN':
      return { ...state, token: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    
    case 'LOGOUT':
      console.log('🔄 Reducer: Ejecutando LOGOUT');
      return {
        ...initialState,
        isLoading: false,
      };
    
    default:
      return state;
  }
};

// Crear el contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Props del provider
interface AuthProviderProps {
  children: ReactNode;
}

// Provider del contexto
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Función para manejar errores
  const handleError = (error: any) => {
    let errorMessage = 'Ha ocurrido un error inesperado';
    
    if (error && typeof error === 'object') {
      if (error.message) {
        errorMessage = error.message;
      } else if (error.status === 401) {
        errorMessage = 'Credenciales incorrectas';
      } else if (error.status === 400) {
        errorMessage = 'Datos inválidos';
      } else if (error.status === 0) {
        errorMessage = 'Error de conexión';
      }
    }
    
    dispatch({ type: 'SET_ERROR', payload: errorMessage });
  };

  // Función de login
  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const response = await authService.login(credentials);
      
      const user: User = {
        id: response.id,
        username: response.username,
        email: response.email,
        roles: response.roles,
      };
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token: response.token }
      });

      // Redireccionar al dashboard después del login exitoso
      router.replace('/(tabs)/');
      
    } catch (error) {
      handleError(error);
    }
  };

  // Función de registro
  const signup = async (userData: SignupRequest): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      await authService.signup(userData);
      
      // Después del registro exitoso, hacer login automático
      await login({
        username: userData.username,
        password: userData.password,
      });

      // La redirección se maneja en la función login
      
    } catch (error) {
      handleError(error);
    }
  };

  // Función de logout
  const logout = async (): Promise<void> => {
    console.log('🔄 Iniciando logout...');
    try {
      console.log('📡 Llamando al servicio de logout...');
      await authService.logout();
      console.log('✅ Servicio de logout completado');
    } catch (error) {
      console.warn('❌ Error al cerrar sesión:', error);
    } finally {
      console.log('🔄 Actualizando estado y redirigiendo...');
      dispatch({ type: 'LOGOUT' });
      // Forzar redirección a la pantalla de autenticación
      router.replace('/auth');
      console.log('✅ Logout completado');
    }
  };

  // Función para limpiar errores
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Función para verificar sesión
  const checkSession = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const hasSession = await authService.hasActiveSession();
      
      if (hasSession) {
        const sessionInfo = await authService.getSessionInfo();
        const user: User = {
          id: sessionInfo.id,
          username: sessionInfo.username,
          email: sessionInfo.email,
          roles: sessionInfo.roles,
        };
        
        const token = await authService.getToken();
        
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user, token: token || '' } 
        });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
      
    } catch (error) {
      // Si hay error verificando la sesión, limpiar datos
      await authService.clearAuthData();
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Verificar sesión al montar el componente
  useEffect(() => {
    checkSession();
  }, []);

  // Valor del contexto
  const contextValue: AuthContextType = {
    user: state.user,
    token: state.token,
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    login,
    signup,
    logout,
    clearError,
    checkSession,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook para usar el contexto
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  
  return context;
};

export default AuthContext;
