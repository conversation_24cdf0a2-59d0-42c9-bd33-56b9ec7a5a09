/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        // Hacer IBM Plex Sans Light la fuente por defecto (Web)
        'sans': ['"IBM Plex Sans"', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', 'sans-serif'],
        'ibm': ['"IBM Plex Sans"', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', 'sans-serif'],
        // Fuentes específicas para React Native
        'ibm-thin': ['IBMPlexSans-Thin'],
        'ibm-extralight': ['IBMPlexSans-ExtraLight'],
        'ibm-light': ['IBMPlexSans-Light'],
        'ibm-regular': ['IBMPlexSans-Regular'],
        'ibm-medium': ['IBMPlexSans-Medium'],
        'ibm-semibold': ['IBMPlexSans-SemiBold'],
        'ibm-bold': ['IBMPlexSans-Bold'],
      },
      fontWeight: {
        'thin': '100',
        'extralight': '200',
        'light': '300',
        'normal': '300', // Cambiar default a Light (300)
        'medium': '500',
        'semibold': '600',
        'bold': '700',
      }
    },
  },
  plugins: [],
}

