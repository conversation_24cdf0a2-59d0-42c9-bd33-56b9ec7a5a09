Running application "main" with appParams:
 {rootTag: '#root', hydrate: undefined} 
Development-level warnings: ON.
Performance optimizations: OFF.
_layout.tsx:1  "shadow*" style props are deprecated. Use "boxShadow".
warnOnce @ index.js:24
preprocess @ preprocess.js:136
compileAndInsertAtomic @ index.js:56
(anonymous) @ index.js:99
create @ index.js:87
(anonymous) @ Sitemap.js:144
loadModuleImplementation @ require.js:277
guardedLoadModule @ require.js:184
metroRequire @ require.js:92
(anonymous) @ exports.js:63
loadModuleImplementation @ require.js:277
guardedLoadModule @ require.js:184
metroRequire @ require.js:92
(anonymous) @ index.js:22
loadModuleImplementation @ require.js:277
guardedLoadModule @ require.js:184
metroRequire @ require.js:92
(anonymous) @ _layout.tsx:1
loadModuleImplementation @ require.js:277
guardedLoadModule @ require.js:177
metroRequire @ require.js:92
get @ app:3
metroContext @ app:21
loadRoute @ getRoutesCore.js:163
getDirectoryTree @ getRoutesCore.js:222
getRoutes @ getRoutesCore.js:23
getRoutes @ getRoutes.js:20
useStore @ router-store.js:132
ContextNavigator @ ExpoRoot.js:123
react-stack-bottom-frame @ react-dom-client.development.js:22428
renderWithHooks @ react-dom-client.development.js:5757
updateFunctionComponent @ react-dom-client.development.js:8018
beginWork @ react-dom-client.development.js:9683
runWithFiberInDEV @ react-dom-client.development.js:543
performUnitOfWork @ react-dom-client.development.js:15044
workLoopConcurrent @ react-dom-client.development.js:15038
renderRootConcurrent @ react-dom-client.development.js:15013
performWorkOnRoot @ react-dom-client.development.js:14333
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:15931
performWorkUntilDeadline @ scheduler.development.js:44
[NEW] Explain Console errors by using Copilot in Edge: click
         
         to explain an error. 
        Learn more
        Don't show again
index.js:24  props.pointerEvents is deprecated. Use style.pointerEvents
warnOnce @ index.js:24
createDOMProps @ index.js:805
createElement @ index.js:23
View @ index.js:111
react-stack-bottom-frame @ react-dom-client.development.js:22428
renderWithHooks @ react-dom-client.development.js:5757
updateForwardRef @ react-dom-client.development.js:7762
beginWork @ react-dom-client.development.js:10014
runWithFiberInDEV @ react-dom-client.development.js:543
performUnitOfWork @ react-dom-client.development.js:15044
workLoopSync @ react-dom-client.development.js:14870
renderRootSync @ react-dom-client.development.js:14850
performWorkOnRoot @ react-dom-client.development.js:14334
performSyncWorkOnRoot @ react-dom-client.development.js:15946
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:15807
processRootScheduleInMicrotask @ react-dom-client.development.js:15841
(anonymous) @ react-dom-client.development.js:15962
admin:1  Blocked aria-hidden on an element because its descendant retained focus. The focus must not be hidden from assistive technology users. Avoid using aria-hidden on a focused element or its ancestor. Consider using the inert attribute instead, which will also prevent focus. For more details, see the aria-hidden section of the WAI-ARIA specification at https://w3c.github.io/aria/#aria-hidden.
Element with focus: <div.css-view-g5y9jx r-transitionProperty-1i6wzkk r-userSelect-lrvibr r-cursor-1loqt21 r-touchAction-1otgn73>
Ancestor with aria-hidden: <div.css-view-g5y9jx r-flex-13awgt0 r-bottom-1p0dtai r-left-1d2f490 r-position-u8s1d r-right-zchlnj r-top-ipm5af> <div class=​"css-view-g5y9jx r-flex-13awgt0 r-bottom-1p0dtai r-left-1d2f490 r-position-u8s1d r-right-zchlnj r-top-ipm5af" style=​"background-color:​ rgb(1, 1, 1)​;​ display:​ flex;​" aria-hidden=​"true">​…​</div>​
admin/vendors:1  Blocked aria-hidden on an element because its descendant retained focus. The focus must not be hidden from assistive technology users. Avoid using aria-hidden on a focused element or its ancestor. Consider using the inert attribute instead, which will also prevent focus. For more details, see the aria-hidden section of the WAI-ARIA specification at https://w3c.github.io/aria/#aria-hidden.
Element with focus: <div.css-view-g5y9jx r-transitionProperty-1i6wzkk r-userSelect-lrvibr r-cursor-1loqt21 r-touchAction-1otgn73 r-backgroundColor-14lw9ot r-borderRadius-1xfd6ze r-boxShadow-9rmwhf r-marginBottom-1ifxtd0 r-padding-nsbfu8 r-width-a1w0r5>
Ancestor with aria-hidden: <div.css-view-g5y9jx r-flex-13awgt0 r-bottom-1p0dtai r-left-1d2f490 r-position-u8s1d r-right-zchlnj r-top-ipm5af> <div class=​"css-view-g5y9jx r-flex-13awgt0 r-bottom-1p0dtai r-left-1d2f490 r-position-u8s1d r-right-zchlnj r-top-ipm5af" aria-hidden=​"true" style=​"background-color:​ rgb(1, 1, 1)​;​ display:​ none;​">​…​</div>​
admin/vendors:1  Access to fetch at 'http://localhost:8080/api/admin/usuarios/vendedores' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
vendorService.ts:25 
            
            
            GET http://localhost:8080/api/admin/usuarios/vendedores net::ERR_FAILED
makeRequest @ vendorService.ts:25
getVendors @ vendorService.ts:61
loadVendors @ index.tsx:44
(anonymous) @ index.tsx:56
react-stack-bottom-frame @ react-dom-client.development.js:22509
runWithFiberInDEV @ react-dom-client.development.js:543
commitHookEffectListMount @ react-dom-client.development.js:10758
commitHookPassiveMountEffects @ react-dom-client.development.js:10878
reconnectPassiveEffects @ react-dom-client.development.js:12802
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12774
commitPassiveMountOnFiber @ react-dom-client.development.js:12731
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12723
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12646
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12723
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627
commitPassiveMountOnFiber @ react-dom-client.development.js:12755
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12627