/**
 * Tipos TypeScript para autenticación
 */

// Enum para roles de usuario
export enum UserRole {
  ADMIN = 'ROLE_ADMIN',
  VENDEDOR = 'ROLE_VENDEDOR',
  USUARIO = 'ROLE_USUARIO',
}

// Interface para el usuario
export interface User {
  id: number;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
}

// DTOs para requests
export interface LoginRequest {
  username: string;
  password: string;
}

export interface SignupRequest {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  roles?: string[];
}

// DTOs para gestión de vendedores
export interface CreateVendedorRequest {
  username: string;      // Requerido, min 3, max 20 caracteres
  email: string;         // Requerido, formato email válido, max 50 caracteres
  password: string;      // Requerido, min 6, max 40 caracteres
  firstName?: string;    // Opcional, max 50 caracteres
  lastName?: string;     // Opcional, max 50 caracteres
}

export interface UserDetailResponse {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  active: boolean;
  roles: string[];
}

// DTOs para responses
export interface JwtResponse {
  token: string;
  type: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

export interface MessageResponse {
  message: string;
}

export interface SessionInfoResponse {
  token?: string | null;
  type: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

// Estados de autenticación
export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

// Context type para autenticación
export interface AuthContextType {
  // Estado
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  
  // Acciones
  login: (credentials: LoginRequest) => Promise<void>;
  signup: (userData: SignupRequest) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  checkSession: () => Promise<void>;
}

// Tipos para errores de API
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Tipos para validación de formularios
export interface FormErrors {
  username?: string;
  email?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  general?: string;
}

// Tipos para el estado de los formularios
export interface FormState {
  isSubmitting: boolean;
  errors: FormErrors;
}

// Utilidades para roles
export const getRoleDisplayName = (role: string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return 'Administrador';
    case UserRole.VENDEDOR:
      return 'Vendedor';
    case UserRole.USUARIO:
      return 'Usuario';
    default:
      return 'Usuario';
  }
};

export const getRoleColor = (role: string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return '#d13438'; // Rojo
    case UserRole.VENDEDOR:
      return '#107c10'; // Verde
    case UserRole.USUARIO:
      return '#0078d4'; // Azul
    default:
      return '#605e5c'; // Gris
  }
};

export const hasRole = (userRoles: string[], requiredRole: UserRole): boolean => {
  return userRoles.includes(requiredRole);
};

export const hasAnyRole = (userRoles: string[], requiredRoles: UserRole[]): boolean => {
  return requiredRoles.some(role => userRoles.includes(role));
};
