/**
 * Sistema de colores Windows Phone UI
 * Basado en la paleta oficial de Windows Phone con soporte para modo claro y oscuro
 */

// Colores principales de Windows Phone
export const WindowsPhoneColors = {
  // Colores de acento principales (Windows Phone signature colors)
  accent: {
    blue: '#0078D4',        // Microsoft Blue (principal)
    cyan: '#00BCF2',        // Cyan vibrante
    teal: '#00B7C3',        // Teal
    green: '#107C10',       // Verde
    lime: '#8CBF26',        // Lima
    yellow: '#FFB900',      // Amarillo
    orange: '#FF8C00',      // Naranja
    red: '#E74856',         // Rojo
    pink: '#E3008C',        // Rosa
    purple: '#881798',      // Púrpura
    indigo: '#6B69D6',      // Índigo
    violet: '#8764B8',      // Violeta
  },

  // Colores de estado
  status: {
    success: '#107C10',     // Verde para éxito
    warning: '#FFB900',     // Amarillo para advertencias
    error: '#E74856',       // Rojo para errores
    info: '#0078D4',        // Azul para información
  },

  // Escala de grises (neutral)
  neutral: {
    // Blancos y grises claros
    white: '#FFFFFF',
    gray10: '#FAF9F8',
    gray20: '#F3F2F1',
    gray30: '#EDEBE9',
    gray40: '#E1DFDD',
    gray50: '#D2D0CE',
    gray60: '#C8C6C4',
    
    // Grises medios
    gray70: '#BEBBB8',
    gray80: '#B3B0AD',
    gray90: '#A19F9D',
    gray100: '#979593',
    gray110: '#8A8886',
    gray120: '#797775',
    
    // Grises oscuros
    gray130: '#605E5C',
    gray140: '#484644',
    gray150: '#323130',
    gray160: '#252423',
    gray170: '#201F1E',
    gray180: '#1B1A19',
    gray190: '#161514',
    gray200: '#11100F',
    black: '#000000',
  },

  // Tema claro (Light Theme)
  light: {
    // Fondos
    background: {
      primary: '#FFFFFF',     // Fondo principal
      secondary: '#F3F2F1',   // Fondo secundario
      tertiary: '#FAF9F8',    // Fondo terciario
      accent: '#0078D4',      // Fondo de acento
      card: '#FFFFFF',        // Fondo de tarjetas
      overlay: 'rgba(0, 0, 0, 0.4)', // Overlay para modales
    },
    
    // Textos
    text: {
      primary: '#323130',     // Texto principal
      secondary: '#605E5C',   // Texto secundario
      tertiary: '#8A8886',    // Texto terciario
      accent: '#0078D4',      // Texto de acento
      onAccent: '#FFFFFF',    // Texto sobre acento
      disabled: '#A19F9D',    // Texto deshabilitado
      inverse: '#FFFFFF',     // Texto inverso
    },
    
    // Bordes
    border: {
      primary: '#E1DFDD',     // Borde principal
      secondary: '#D2D0CE',   // Borde secundario
      accent: '#0078D4',      // Borde de acento
      focus: '#0078D4',       // Borde de foco
      error: '#E74856',       // Borde de error
      disabled: '#C8C6C4',    // Borde deshabilitado
    },
    
    // Superficies interactivas
    surface: {
      default: '#FFFFFF',
      hover: '#F3F2F1',
      active: '#EDEBE9',
      selected: '#E1DFDD',
      disabled: '#F3F2F1',
    },
  },

  // Tema oscuro (Dark Theme)
  dark: {
    // Fondos
    background: {
      primary: '#1B1A19',     // Fondo principal
      secondary: '#252423',   // Fondo secundario
      tertiary: '#323130',    // Fondo terciario
      accent: '#0078D4',      // Fondo de acento
      card: '#252423',        // Fondo de tarjetas
      overlay: 'rgba(0, 0, 0, 0.6)', // Overlay para modales
    },
    
    // Textos
    text: {
      primary: '#FFFFFF',     // Texto principal
      secondary: '#D2D0CE',   // Texto secundario
      tertiary: '#A19F9D',    // Texto terciario
      accent: '#60CDFF',      // Texto de acento (más claro en dark)
      onAccent: '#000000',    // Texto sobre acento
      disabled: '#605E5C',    // Texto deshabilitado
      inverse: '#323130',     // Texto inverso
    },
    
    // Bordes
    border: {
      primary: '#484644',     // Borde principal
      secondary: '#605E5C',   // Borde secundario
      accent: '#60CDFF',      // Borde de acento
      focus: '#60CDFF',       // Borde de foco
      error: '#FF6B6B',       // Borde de error
      disabled: '#484644',    // Borde deshabilitado
    },
    
    // Superficies interactivas
    surface: {
      default: '#252423',
      hover: '#323130',
      active: '#484644',
      selected: '#605E5C',
      disabled: '#323130',
    },
  },
};

// Colores semánticos para componentes específicos
export const ComponentColors = {
  button: {
    primary: {
      background: WindowsPhoneColors.accent.blue,
      text: WindowsPhoneColors.light.text.onAccent,
      border: WindowsPhoneColors.accent.blue,
    },
    secondary: {
      background: WindowsPhoneColors.light.background.secondary,
      text: WindowsPhoneColors.light.text.primary,
      border: WindowsPhoneColors.light.border.primary,
    },
    outline: {
      background: 'transparent',
      text: WindowsPhoneColors.accent.blue,
      border: WindowsPhoneColors.accent.blue,
    },
    ghost: {
      background: 'transparent',
      text: WindowsPhoneColors.light.text.primary,
      border: 'transparent',
    },
  },
  
  input: {
    background: WindowsPhoneColors.light.background.primary,
    text: WindowsPhoneColors.light.text.primary,
    placeholder: WindowsPhoneColors.light.text.tertiary,
    border: WindowsPhoneColors.light.border.primary,
    borderFocus: WindowsPhoneColors.light.border.focus,
    borderError: WindowsPhoneColors.light.border.error,
  },
  
  navigation: {
    background: WindowsPhoneColors.light.background.primary,
    text: WindowsPhoneColors.light.text.secondary,
    textActive: WindowsPhoneColors.accent.blue,
    indicator: WindowsPhoneColors.accent.blue,
    border: WindowsPhoneColors.light.border.primary,
  },
};

// Utilidades para obtener colores según el tema
export const getThemeColors = (isDark: boolean = false) => {
  return isDark ? WindowsPhoneColors.dark : WindowsPhoneColors.light;
};

// Función para obtener color de acento con opacidad
export const getAccentColor = (opacity: number = 1) => {
  const color = WindowsPhoneColors.accent.blue;
  if (opacity === 1) return color;
  
  // Convertir hex a rgba
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

// Función para obtener color neutral por nivel
export const getNeutralColor = (level: keyof typeof WindowsPhoneColors.neutral) => {
  return WindowsPhoneColors.neutral[level];
};

// Exportar también los colores originales para compatibilidad
export const FluentColors = {
  primary: WindowsPhoneColors.accent.blue,
  secondary: WindowsPhoneColors.accent.indigo,
  success: WindowsPhoneColors.status.success,
  warning: WindowsPhoneColors.status.warning,
  error: WindowsPhoneColors.status.error,
  neutral: WindowsPhoneColors.neutral,
};

export default WindowsPhoneColors;
