/**
 * Sistema de Colores Fluent UI Expandido
 * Basado en Microsoft Fluent UI Design System
 * Incluye soporte para modo claro y oscuro
 */

// Colores principales del sistema
export const FluentColorTokens = {
  // Colores de marca/brand
  brand: {
    primary: '#0078D4',      // Azul principal de Microsoft
    secondary: '#6B69D6',    // Púrpura secundario
    tertiary: '#0099BC',     // Azul terciario
  },

  // Colores semánticos
  semantic: {
    success: '#107C10',      // Verde éxito
    warning: '#FF8C00',      // Naranja advertencia
    error: '#D13438',        // Rojo error
    info: '#0078D4',         // Azul información
  },

  // Escala de grises neutral
  neutral: {
    // Grises claros (backgrounds)
    gray10: '#FAF9F8',
    gray20: '#F3F2F1',
    gray30: '#EDEBE9',
    gray40: '#E1DFDD',
    gray50: '#D2D0CE',
    gray60: '#C8C6C4',
    
    // Grises medios (borders, disabled)
    gray70: '#BEBBB8',
    gray80: '#B3B0AD',
    gray90: '#A19F9D',
    gray100: '#979593',
    gray110: '#8A8886',
    gray120: '#797775',
    
    // Grises oscuros (text)
    gray130: '#605E5C',
    gray140: '#484644',
    gray150: '#323130',
    gray160: '#252423',
    gray170: '#201F1E',
    gray180: '#1B1A19',
    gray190: '#161514',
    gray200: '#11100F',
  },

  // Colores de acento adicionales
  accent: {
    blue: '#0078D4',
    purple: '#6B69D6',
    teal: '#0099BC',
    green: '#107C10',
    orange: '#FF8C00',
    red: '#D13438',
    pink: '#E3008C',
    indigo: '#5C2E91',
  }
} as const;

// Tokens para modo claro
export const LightTheme = {
  // Backgrounds
  background: {
    primary: FluentColorTokens.neutral.gray10,     // #FAF9F8
    secondary: FluentColorTokens.neutral.gray20,   // #F3F2F1
    tertiary: FluentColorTokens.neutral.gray30,    // #EDEBE9
    canvas: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.4)',
  },

  // Superficies (cards, containers)
  surface: {
    primary: '#FFFFFF',
    secondary: FluentColorTokens.neutral.gray10,   // #FAF9F8
    tertiary: FluentColorTokens.neutral.gray20,    // #F3F2F1
    elevated: '#FFFFFF',
  },

  // Texto
  text: {
    primary: FluentColorTokens.neutral.gray160,    // #252423
    secondary: FluentColorTokens.neutral.gray130,  // #605E5C
    tertiary: FluentColorTokens.neutral.gray110,   // #8A8886
    disabled: FluentColorTokens.neutral.gray90,    // #A19F9D
    inverse: '#FFFFFF',
    accent: FluentColorTokens.brand.primary,       // #0078D4
  },

  // Bordes
  border: {
    primary: FluentColorTokens.neutral.gray60,     // #C8C6C4
    secondary: FluentColorTokens.neutral.gray40,   // #E1DFDD
    tertiary: FluentColorTokens.neutral.gray30,    // #EDEBE9
    focus: FluentColorTokens.brand.primary,        // #0078D4
    error: FluentColorTokens.semantic.error,       // #D13438
  },

  // Estados interactivos
  interactive: {
    primary: FluentColorTokens.brand.primary,      // #0078D4
    primaryHover: '#106EBE',
    primaryPressed: '#005A9E',
    primaryDisabled: FluentColorTokens.neutral.gray90,

    secondary: FluentColorTokens.neutral.gray20,   // #F3F2F1
    secondaryHover: FluentColorTokens.neutral.gray30,
    secondaryPressed: FluentColorTokens.neutral.gray40,
    
    subtle: 'transparent',
    subtleHover: FluentColorTokens.neutral.gray20,
    subtlePressed: FluentColorTokens.neutral.gray30,
  },

  // Colores semánticos aplicados
  status: {
    success: FluentColorTokens.semantic.success,
    successBackground: '#DFF6DD',
    warning: FluentColorTokens.semantic.warning,
    warningBackground: '#FFF4CE',
    error: FluentColorTokens.semantic.error,
    errorBackground: '#FDE7E9',
    info: FluentColorTokens.semantic.info,
    infoBackground: '#D1E7FF',
  }
} as const;

// Tokens para modo oscuro
export const DarkTheme = {
  // Backgrounds
  background: {
    primary: FluentColorTokens.neutral.gray190,    // #161514
    secondary: FluentColorTokens.neutral.gray180,  // #1B1A19
    tertiary: FluentColorTokens.neutral.gray170,   // #201F1E
    canvas: FluentColorTokens.neutral.gray200,     // #11100F
    overlay: 'rgba(0, 0, 0, 0.6)',
  },

  // Superficies
  surface: {
    primary: FluentColorTokens.neutral.gray180,    // #1B1A19
    secondary: FluentColorTokens.neutral.gray170,  // #201F1E
    tertiary: FluentColorTokens.neutral.gray160,   // #252423
    elevated: FluentColorTokens.neutral.gray170,   // #201F1E
  },

  // Texto
  text: {
    primary: '#FFFFFF',
    secondary: FluentColorTokens.neutral.gray60,   // #C8C6C4
    tertiary: FluentColorTokens.neutral.gray90,    // #A19F9D
    disabled: FluentColorTokens.neutral.gray120,   // #797775
    inverse: FluentColorTokens.neutral.gray160,    // #252423
    accent: '#4CC2FF',                             // Azul más claro para contraste
  },

  // Bordes
  border: {
    primary: FluentColorTokens.neutral.gray120,    // #797775
    secondary: FluentColorTokens.neutral.gray140,  // #484644
    tertiary: FluentColorTokens.neutral.gray150,   // #323130
    focus: '#4CC2FF',
    error: '#FF6B6B',
  },

  // Estados interactivos
  interactive: {
    primary: '#4CC2FF',
    primaryHover: '#6BD0FF',
    primaryPressed: '#2BB8FF',
    primaryDisabled: FluentColorTokens.neutral.gray120,

    secondary: FluentColorTokens.neutral.gray160,  // #252423
    secondaryHover: FluentColorTokens.neutral.gray150,
    secondaryPressed: FluentColorTokens.neutral.gray140,
    
    subtle: 'transparent',
    subtleHover: FluentColorTokens.neutral.gray170,
    subtlePressed: FluentColorTokens.neutral.gray160,
  },

  // Colores semánticos aplicados
  status: {
    success: '#6CCB5F',
    successBackground: '#393D1B',
    warning: '#FCE100',
    warningBackground: '#433519',
    error: '#FF6B6B',
    errorBackground: '#442726',
    info: '#4CC2FF',
    infoBackground: '#1B2F4C',
  }
} as const;

// Exportación de colores legacy para compatibilidad
export const FluentColors = {
  ...FluentColorTokens.brand,
  ...FluentColorTokens.semantic,
  neutral: FluentColorTokens.neutral,
  accent: FluentColorTokens.accent,
} as const;

// Tipos para TypeScript
export type FluentColorToken = typeof FluentColorTokens;
export type LightThemeColors = typeof LightTheme;
export type DarkThemeColors = typeof DarkTheme;
export type ThemeColors = LightThemeColors | DarkThemeColors;

// Utilidad para obtener el tema actual
export const getThemeColors = (isDark: boolean): ThemeColors => {
  return isDark ? DarkTheme : LightTheme;
};

// Utilidades para acceso rápido a colores comunes
export const CommonColors = {
  // Colores más usados para acceso rápido
  primary: FluentColorTokens.brand.primary,
  white: '#FFFFFF',
  black: FluentColorTokens.neutral.gray200,
  transparent: 'transparent',
  
  // Overlays comunes
  overlay: {
    light: 'rgba(255, 255, 255, 0.8)',
    medium: 'rgba(0, 0, 0, 0.4)',
    dark: 'rgba(0, 0, 0, 0.6)',
  },
  
  // Sombras
  shadow: {
    light: 'rgba(0, 0, 0, 0.1)',
    medium: 'rgba(0, 0, 0, 0.2)',
    dark: 'rgba(0, 0, 0, 0.3)',
  }
} as const;

export default FluentColors;
