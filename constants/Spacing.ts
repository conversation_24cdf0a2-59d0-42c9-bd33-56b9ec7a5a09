/**
 * Sistema de espaciado Windows Phone UI
 * Basado en una escala de 8px para consistencia visual
 */

// Escala base de espaciado (múltiplos de 8px)
export const SpacingScale = {
  xs: 4,    // 0.25rem - Espaciado extra pequeño
  sm: 8,    // 0.5rem  - Espaciado pequeño
  md: 16,   // 1rem    - Espaciado medio (base)
  lg: 24,   // 1.5rem  - Espaciado grande
  xl: 32,   // 2rem    - Espaciado extra grande
  '2xl': 40, // 2.5rem  - Espaciado 2x grande
  '3xl': 48, // 3rem    - Espaciado 3x grande
  '4xl': 64, // 4rem    - Espaciado 4x grande
  '5xl': 80, // 5rem    - Espaciado 5x grande
  '6xl': 96, // 6rem    - Espaciado 6x grande
} as const;

// Espaciado específico para componentes
export const ComponentSpacing = {
  // Padding interno de componentes
  button: {
    small: { horizontal: SpacingScale.md, vertical: SpacingScale.xs },
    medium: { horizontal: SpacingScale.lg, vertical: SpacingScale.sm },
    large: { horizontal: SpacingScale.xl, vertical: SpacingScale.md },
  },
  
  input: {
    small: { horizontal: SpacingScale.sm, vertical: SpacingScale.xs },
    medium: { horizontal: SpacingScale.md, vertical: SpacingScale.sm },
    large: { horizontal: SpacingScale.lg, vertical: SpacingScale.md },
  },
  
  card: {
    padding: SpacingScale.lg,
    margin: SpacingScale.md,
    gap: SpacingScale.md,
  },
  
  // Espaciado de layout
  layout: {
    screenPadding: SpacingScale.lg,     // Padding lateral de pantallas
    sectionGap: SpacingScale.xl,        // Espacio entre secciones
    itemGap: SpacingScale.md,           // Espacio entre elementos
    listItemGap: SpacingScale.sm,       // Espacio entre items de lista
  },
  
  // Navegación
  navigation: {
    height: 56,                         // Altura de barra de navegación
    padding: SpacingScale.sm,           // Padding interno
    iconSize: 20,                       // Tamaño de iconos
    textMargin: SpacingScale.xs,        // Margen del texto
  },
  
  // Formularios
  form: {
    fieldGap: SpacingScale.lg,          // Espacio entre campos
    labelMargin: SpacingScale.xs,       // Margen del label
    helperTextMargin: SpacingScale.xs,  // Margen del texto de ayuda
    buttonMargin: SpacingScale.xl,      // Margen superior de botones
  },
};

// Radios de borde (border radius)
export const BorderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 6,
  lg: 8,
  xl: 12,
  '2xl': 16,
  '3xl': 24,
  full: 9999,
} as const;

// Sombras (elevación)
export const Shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
} as const;

// Utilidades para obtener espaciado
export const getSpacing = (size: keyof typeof SpacingScale) => {
  return SpacingScale[size];
};

// Utilidad para crear padding/margin objects
export const createSpacing = (
  top?: number,
  right?: number,
  bottom?: number,
  left?: number
) => {
  if (top !== undefined && right === undefined && bottom === undefined && left === undefined) {
    // Si solo se pasa un valor, aplicar a todos los lados
    return {
      paddingTop: top,
      paddingRight: top,
      paddingBottom: top,
      paddingLeft: top,
    };
  }
  
  return {
    paddingTop: top || 0,
    paddingRight: right || 0,
    paddingBottom: bottom || 0,
    paddingLeft: left || 0,
  };
};

// Utilidad para crear margin objects
export const createMargin = (
  top?: number,
  right?: number,
  bottom?: number,
  left?: number
) => {
  if (top !== undefined && right === undefined && bottom === undefined && left === undefined) {
    return {
      marginTop: top,
      marginRight: top,
      marginBottom: top,
      marginLeft: top,
    };
  }
  
  return {
    marginTop: top || 0,
    marginRight: right || 0,
    marginBottom: bottom || 0,
    marginLeft: left || 0,
  };
};

// Presets comunes de espaciado
export const SpacingPresets = {
  // Padding para contenedores
  containerPadding: createSpacing(SpacingScale.lg),
  cardPadding: createSpacing(SpacingScale.lg),
  buttonPadding: createSpacing(SpacingScale.sm, SpacingScale.lg),
  inputPadding: createSpacing(SpacingScale.sm, SpacingScale.md),
  
  // Margins comunes
  sectionMargin: createMargin(SpacingScale.xl, 0),
  itemMargin: createMargin(SpacingScale.md, 0),
  buttonMargin: createMargin(SpacingScale.lg, 0, 0, 0),
  
  // Gaps para flexbox/grid
  flexGaps: {
    small: SpacingScale.sm,
    medium: SpacingScale.md,
    large: SpacingScale.lg,
  },
};

// Breakpoints para responsive design
export const Breakpoints = {
  sm: 640,   // Teléfonos pequeños
  md: 768,   // Tablets
  lg: 1024,  // Laptops
  xl: 1280,  // Desktops
  '2xl': 1536, // Pantallas grandes
} as const;

// Utilidad para obtener estilos responsive
export const getResponsiveSpacing = (
  mobile: keyof typeof SpacingScale,
  tablet?: keyof typeof SpacingScale,
  desktop?: keyof typeof SpacingScale
) => {
  return {
    base: SpacingScale[mobile],
    md: tablet ? SpacingScale[tablet] : SpacingScale[mobile],
    lg: desktop ? SpacingScale[desktop] : tablet ? SpacingScale[tablet] : SpacingScale[mobile],
  };
};

export default {
  SpacingScale,
  ComponentSpacing,
  BorderRadius,
  Shadows,
  SpacingPresets,
  Breakpoints,
  getSpacing,
  createSpacing,
  createMargin,
  getResponsiveSpacing,
};
