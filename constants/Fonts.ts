/**
 * Configuración de fuentes para IBM Plex Sans
 */

// Configuración de fuentes para React Native
export const FontFamily = {
  IBMPlexSansThin: 'IBMPlexSans-Thin',
  IBMPlexSansExtraLight: 'IBMPlexSans-ExtraLight',
  IBMPlexSansLight: 'IBMPlexSans-Light',
  IBMPlexSansRegular: 'IBMPlexSans-Regular',
  IBMPlexSansMedium: 'IBMPlexSans-Medium',
  IBMPlexSansSemiBold: 'IBMPlexSans-SemiBold',
  IBMPlexSansBold: 'IBMPlexSans-Bold',
  // Alias para compatibilidad - usar Light como default
  IBMPlexSans: 'IBMPlexSans-Light',
} as const;

// Pesos de fuente disponibles
export const FontWeight = {
  thin: '100',
  extraLight: '200',
  light: '300',
  regular: '400',
  medium: '500',
  semiBold: '600',
  bold: '700',
} as const;

// Configuración para web (CSS)
export const WebFontConfig = {
  fontFamily: '"IBM Plex Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontDisplay: 'swap',
  fontOpticalSizing: 'auto',
  fontVariationSettings: '"wdth" 100',
} as const;

// Clases CSS para diferentes pesos
export const FontClasses = {
  thin: 'font-ibm-thin',
  extraLight: 'font-ibm-extralight',
  light: 'font-ibm-light',
  regular: 'font-ibm-regular',
  medium: 'font-ibm-medium',
  semiBold: 'font-ibm-semibold',
  bold: 'font-ibm-bold',
} as const;

// Utilidades para React Native
export const getFontStyle = (weight: keyof typeof FontWeight) => {
  const fontFamilyMap = {
    thin: FontFamily.IBMPlexSansThin,
    extraLight: FontFamily.IBMPlexSansExtraLight,
    light: FontFamily.IBMPlexSansLight,
    regular: FontFamily.IBMPlexSansRegular,
    medium: FontFamily.IBMPlexSansMedium,
    semiBold: FontFamily.IBMPlexSansSemiBold,
    bold: FontFamily.IBMPlexSansBold,
  };

  return {
    fontFamily: fontFamilyMap[weight] || FontFamily.IBMPlexSansLight,
  };
};

// Utilidades para web
export const getWebFontClass = (weight: keyof typeof FontWeight) => {
  return FontClasses[weight] || FontClasses.regular;
};
