# Implementación de Autenticación en React Native

Esta documentación describe la implementación completa de autenticación JWT en la aplicación React Native de ecommerce.

## 🚀 Características Implementadas

### ✅ Funcionalidades de Autenticación
- **Login de usuarios** con validación de formularios
- **Registro de nuevos usuarios** con campos opcionales
- **Perfil de usuario** con información detallada y roles
- **Logout seguro** con limpieza de tokens
- **Manejo de sesiones** con JWT tokens y AsyncStorage
- **Protección de rutas** automática
- **Gestión de errores** con mensajes informativos

### 🎨 Componentes de UI
- **LoginForm**: Formulario de inicio de sesión con Tailwind CSS
- **SignupForm**: Formulario de registro con validación
- **UserProfile**: Panel de información del usuario con roles visuales
- **AuthContainer**: Contenedor principal que maneja el estado de autenticación
- **ProtectedRoute**: Componente para proteger rutas autenticadas

### 🔧 Arquitectura Técnica
- **Context API** para manejo de estado global de autenticación
- **TypeScript** con tipos estrictos para la API
- **AsyncStorage** para persistencia de sesión
- **Servicios modulares** para llamadas a la API
- **Configuración centralizada** para endpoints
- **Tailwind CSS** para estilos responsivos

## 📁 Estructura de Archivos

```
commerce/
├── components/
│   └── auth/
│       ├── AuthContainer.tsx      # Contenedor principal
│       ├── LoginForm.tsx          # Formulario de login
│       ├── SignupForm.tsx         # Formulario de registro
│       ├── UserProfile.tsx        # Perfil de usuario
│       ├── ProtectedRoute.tsx     # Protección de rutas
│       └── index.ts              # Exportaciones
├── contexts/
│   └── AuthContext.tsx           # Context de autenticación
├── services/
│   └── authService.ts            # Servicio de API
├── types/
│   └── auth.ts                   # Tipos TypeScript
├── config/
│   └── api.ts                    # Configuración de API
└── app/
    ├── auth/                     # Pantallas de autenticación
    │   ├── _layout.tsx
    │   ├── index.tsx
    │   ├── login.tsx
    │   └── signup.tsx
    └── (tabs)/                   # Pantallas protegidas
        ├── _layout.tsx           # Con ProtectedRoute
        ├── index.tsx             # Home con info de usuario
        ├── explore.tsx
        └── profile.tsx           # Perfil en tab
```

## 🔌 Configuración de la API

### Variables de Entorno
La configuración de la API se encuentra en `config/api.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: 'http://localhost:8080', // Cambiar según el entorno
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/api/auth/login',
      SIGNUP: '/api/auth/signup',
      SESSION_INFO: '/api/auth/session-info',
      LOGOUT: '/api/auth/logout',
    },
  },
};
```

### Endpoints Utilizados
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/signup` - Registrar usuario
- `GET /api/auth/session-info` - Información de sesión
- `POST /api/auth/logout` - Cerrar sesión

## 🎯 Roles de Usuario

La aplicación maneja tres tipos de roles:

| Rol | Descripción | Color |
|-----|-------------|-------|
| **ROLE_ADMIN** | Administrador del sistema | Rojo (#d13438) |
| **ROLE_VENDEDOR** | Vendedor de productos | Verde (#107c10) |
| **ROLE_USUARIO** | Usuario regular | Azul (#0078d4) |

## 🔒 Seguridad

### JWT Token Management
- Los tokens se almacenan en `AsyncStorage`
- Se incluyen automáticamente en las cabeceras de autorización
- Se limpian al cerrar sesión o en caso de error
- Verificación automática de sesión al iniciar la app

### Validación de Formularios
- **Username**: 3-20 caracteres, requerido
- **Email**: Formato válido, máximo 50 caracteres
- **Password**: 6-40 caracteres, requerido
- **Nombres**: Opcionales, sin restricciones específicas

### Protección de Rutas
- Las rutas en `(tabs)` están protegidas automáticamente
- Redirección automática a `/auth` si no está autenticado
- Verificación de sesión en tiempo real

## 🎨 Estilos y UI

### Tailwind CSS
- Clases utilitarias para estilos responsivos
- Soporte para modo oscuro
- Componentes consistentes con el diseño

### Fluent Icons
- Iconos integrados con el sistema existente
- Colores consistentes con los roles
- Tamaños adaptativos

## 🚀 Uso

### Iniciar la Aplicación
```bash
cd commerce
npm start
```

### Credenciales de Prueba
Según la documentación de la API, existe un usuario administrador por defecto:
- **Username**: `admin`
- **Password**: `admin123`

### Flujo de Usuario
1. **Primera visita**: Se muestra la pantalla de autenticación
2. **Login/Registro**: Formularios con validación en tiempo real
3. **Autenticación exitosa**: Acceso a las pantallas protegidas
4. **Navegación**: Tabs con Home, Explore y Perfil
5. **Persistencia**: La sesión se mantiene entre reinicios

## 🔧 Personalización

### Cambiar URL de la API
Modifica el archivo `config/api.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: 'https://tu-api.com',
  // ...
};
```

### Agregar Nuevos Roles
Actualiza el enum en `types/auth.ts`:

```typescript
export enum UserRole {
  ADMIN = 'ROLE_ADMIN',
  VENDEDOR = 'ROLE_VENDEDOR',
  USUARIO = 'ROLE_USUARIO',
  NUEVO_ROL = 'ROLE_NUEVO_ROL'
}
```

### Personalizar Estilos
Los componentes usan Tailwind CSS. Puedes personalizar:
- Colores en `tailwind.config.js`
- Componentes específicos editando las clases
- Temas oscuro/claro en `Colors.ts`

## 🐛 Manejo de Errores

La aplicación maneja varios tipos de errores:

- **401 Unauthorized**: Credenciales incorrectas
- **400 Bad Request**: Datos de formulario inválidos
- **500 Server Error**: Errores del servidor
- **Network Error**: Problemas de conectividad

Los errores se muestran con mensajes informativos en la UI.

## 📱 Responsive Design

Los componentes están diseñados para ser responsivos:
- Formularios adaptativos
- Espaciado consistente
- Soporte para diferentes tamaños de pantalla
- Navegación optimizada para móvil

## 🔄 Estado de la Aplicación

El estado se maneja a través del `AuthContext`:

```typescript
interface AuthContextType {
  user: User | null;           // Usuario actual
  token: string | null;        // JWT token
  isLoading: boolean;          // Estado de carga
  isAuthenticated: boolean;    // Estado de autenticación
  error: string | null;        // Errores actuales
  login: (credentials) => Promise<void>;
  signup: (userData) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  checkSession: () => Promise<void>;
}
```

## 🎯 Próximas Funcionalidades

- [ ] Edición de perfil de usuario
- [ ] Recuperación de contraseña
- [ ] Verificación de email
- [ ] Notificaciones push
- [ ] Biometría para login
- [ ] Configuración de tema personalizado

## 🔗 Integración con la API

Esta implementación está completamente integrada con la API de ecommerce documentada en:
- `docs/Autentificacion.md`
- `docs/AUTHENTICATION_IMPLEMENTATION.md`

Utiliza los mismos endpoints y DTOs que la implementación de React web, garantizando consistencia entre plataformas.
