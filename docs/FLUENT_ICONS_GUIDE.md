# Guía de Iconos Fluent UI para React Native

Esta guía te ayudará a implementar y usar iconos con el estilo de Fluent UI en tu aplicación React Native.

## 📦 Instalación

Los siguientes paquetes ya están instalados en tu proyecto:

```bash
npm install react-native-vector-icons --legacy-peer-deps
npm install react-native-svg --legacy-peer-deps
npm install @fluentui/react-icons --legacy-peer-deps
```

## 🎨 Componentes Disponibles

### 1. FluentIconsDemo
Un componente de demostración que muestra diferentes tipos de iconos usando `react-native-vector-icons`.

```tsx
import FluentIconsDemo from '@/components/FluentIconsDemo';

<FluentIconsDemo />
```

### 2. FluentIconComponent
Un sistema de componentes personalizados que simula el comportamiento de Fluent UI.

```tsx
import { FluentHome, FluentHeart, FluentColors } from '@/components/FluentIconComponent';

<FluentHome size={24} color={FluentColors.primary} />
<FluentHeart size={32} color={FluentColors.error} filled />
```

## 🔧 Uso Básico

### Iconos Predefinidos

```tsx
import {
  FluentHome,
  FluentHeart,
  FluentPerson,
  FluentSettings,
  FluentSearch,
  FluentCart,
  FluentStar,
  FluentMail,
  FluentCalendar,
  FluentDocument,
  FluentColors
} from '@/components/FluentIconComponent';

// Uso básico
<FluentHome size={24} color={FluentColors.primary} />

// Con estado filled/regular
<FluentHeart size={32} color={FluentColors.error} filled={true} />
<FluentHeart size={32} color={FluentColors.error} filled={false} />
```

### Crear Iconos Personalizados

```tsx
import { createFluentIcon } from '@/components/FluentIconComponent';

// Crear un icono personalizado
const FluentMenu = createFluentIcon('menu', 'MaterialIcons');
const FluentLocation = createFluentIcon('location', 'Ionicons');

// Usar el icono personalizado
<FluentMenu size={24} color={FluentColors.neutral.gray120} />
```

## 🎨 Colores Fluent UI

El sistema incluye una paleta de colores oficial de Fluent UI:

```tsx
import { FluentColors } from '@/components/FluentIconComponent';

// Colores principales
FluentColors.primary      // #0078D4
FluentColors.secondary    // #6B69D6
FluentColors.success      // #107C10
FluentColors.warning      // #FF8C00
FluentColors.error        // #D13438

// Escala de grises
FluentColors.neutral.gray10   // #FAF9F8 (más claro)
FluentColors.neutral.gray50   // #D2D0CE
FluentColors.neutral.gray100  // #979593
FluentColors.neutral.gray150  // #323130
FluentColors.neutral.gray200  // #11100F (más oscuro)
```

## 📚 Bibliotecas de Iconos Disponibles

### 1. MaterialIcons
```tsx
<FluentIcon name="home" library="MaterialIcons" size={24} />
```

### 2. MaterialCommunityIcons
```tsx
<FluentIcon name="account-circle" library="MaterialCommunityIcons" size={24} />
```

### 3. Ionicons
```tsx
<FluentIcon name="heart" library="Ionicons" size={24} />
```

## 🔄 Estados Filled vs Regular

Muchos iconos soportan estados filled (relleno) y regular (contorno):

```tsx
// Icono relleno
<FluentHeart filled={true} color={FluentColors.error} />

// Icono con contorno
<FluentHeart filled={false} color={FluentColors.error} />
```

## 📏 Tamaños Recomendados

```tsx
// Tamaños estándar
<FluentIcon size={16} />  // Pequeño
<FluentIcon size={20} />  // Texto
<FluentIcon size={24} />  // Estándar (por defecto)
<FluentIcon size={32} />  // Grande
<FluentIcon size={48} />  // Extra grande
```

## 🎯 Ejemplos Prácticos

### Botón con Icono
```tsx
import { TouchableOpacity, Text } from 'react-native';
import { FluentHome, FluentColors } from '@/components/FluentIconComponent';

<TouchableOpacity className="flex-row items-center p-3 bg-blue-500 rounded-lg">
  <FluentHome size={20} color="white" />
  <Text className="text-white ml-2">Inicio</Text>
</TouchableOpacity>
```

### Lista con Iconos
```tsx
const menuItems = [
  { icon: FluentHome, label: 'Inicio', color: FluentColors.primary },
  { icon: FluentPerson, label: 'Perfil', color: FluentColors.success },
  { icon: FluentSettings, label: 'Configuración', color: FluentColors.neutral.gray120 },
];

{menuItems.map((item, index) => (
  <TouchableOpacity key={index} className="flex-row items-center p-4">
    <item.icon size={24} color={item.color} />
    <Text className="ml-3 text-gray-800">{item.label}</Text>
  </TouchableOpacity>
))}
```

### Navegación con Iconos
```tsx
// En tu navegación de tabs
<Tab.Screen 
  name="Home" 
  component={HomeScreen}
  options={{
    tabBarIcon: ({ focused, color }) => (
      <FluentHome 
        size={24} 
        color={focused ? FluentColors.primary : FluentColors.neutral.gray120}
        filled={focused}
      />
    ),
  }}
/>
```

## 🚀 Mejores Prácticas

1. **Consistencia**: Usa siempre los colores de FluentColors para mantener consistencia
2. **Tamaños**: Usa tamaños estándar (16, 20, 24, 32, 48)
3. **Estados**: Aprovecha los estados filled/regular para indicar selección
4. **Accesibilidad**: Siempre incluye labels descriptivos
5. **Performance**: Usa iconos predefinidos cuando sea posible

## 🔗 Recursos Adicionales

- [Fluent UI Design System](https://developer.microsoft.com/en-us/fluentui)
- [React Native Vector Icons](https://github.com/oblador/react-native-vector-icons)
- [Material Icons](https://fonts.google.com/icons)
- [Ionicons](https://ionic.io/ionicons)

## 🐛 Solución de Problemas

### Los iconos no se muestran
1. Verifica que react-native-vector-icons esté correctamente instalado
2. En iOS, asegúrate de que las fuentes estén vinculadas
3. En Android, verifica la configuración en android/app/build.gradle

### Conflictos de dependencias
Si encuentras conflictos con React 19, usa `--legacy-peer-deps` al instalar paquetes.

### Iconos no encontrados
Verifica que el nombre del icono sea correcto para la biblioteca específica:
- [Material Icons](https://fonts.google.com/icons)
- [Material Community Icons](https://materialdesignicons.com/)
- [Ionicons](https://ionic.io/ionicons)
