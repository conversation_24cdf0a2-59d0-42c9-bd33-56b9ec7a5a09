# Gestión de Vendedores - Documentación para Frontend

Este documento describe la implementación de la funcionalidad de gestión de vendedores para administradores en la plataforma de ecommerce. Esta funcionalidad permite a los usuarios con rol de administrador crear y gestionar usuarios con rol de vendedor.

## Índice

1. [Descripción General](#descripción-general)
2. [Endpoints de la API](#endpoints-de-la-api)
3. [Modelos de Datos](#modelos-de-datos)
4. [Implementación en Frontend](#implementación-en-frontend)
   - [Componentes Sugeridos](#componentes-sugeridos)
   - [Flujo de Trabajo](#flujo-de-trabajo)
   - [Ejemplos de Código](#ejemplos-de-código)
5. [Consideraciones de Seguridad](#consideraciones-de-seguridad)
6. [Pruebas](#pruebas)

## Descripción General

La funcionalidad de gestión de vendedores permite a los administradores:

- Crear nuevos usuarios con rol de vendedor
- Ver una lista de todos los vendedores registrados
- Ver detalles de un vendedor específico
- Activar o desactivar cuentas de vendedor

Esta funcionalidad es exclusiva para usuarios con el rol `ROLE_ADMIN` y está protegida mediante autenticación JWT.

## Endpoints de la API

### Crear un Nuevo Vendedor

**Endpoint:** `POST /api/admin/usuarios/vendedores`  
**Acceso:** Solo usuarios con rol `ROLE_ADMIN`  
**Descripción:** Crea un nuevo usuario con rol de vendedor.

**Request:**
```http
POST /api/admin/usuarios/vendedores HTTP/1.1
Host: localhost:8080
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "username": "vendedor1",
  "email": "<EMAIL>",
  "password": "vendedor123",
  "firstName": "Vendedor",
  "lastName": "Uno"
}
```

**Response (200 OK):**
```json
{
  "id": 3,
  "username": "vendedor1",
  "email": "<EMAIL>",
  "firstName": "Vendedor",
  "lastName": "Uno",
  "active": true,
  "roles": ["ROLE_VENDEDOR"]
}
```

**Response (400 Bad Request):**
```json
{
  "username": "El nombre de usuario ya está en uso"
}
```

### Obtener Lista de Vendedores

**Endpoint:** `GET /api/admin/usuarios/vendedores`  
**Acceso:** Solo usuarios con rol `ROLE_ADMIN`  
**Descripción:** Obtiene la lista de todos los usuarios con rol de vendedor.

**Request:**
```http
GET /api/admin/usuarios/vendedores HTTP/1.1
Host: localhost:8080
Authorization: Bearer {jwt_token}
```

**Response (200 OK):**
```json
[
  {
    "id": 3,
    "username": "vendedor1",
    "email": "<EMAIL>",
    "firstName": "Vendedor",
    "lastName": "Uno",
    "active": true,
    "roles": ["ROLE_VENDEDOR"]
  },
  {
    "id": 4,
    "username": "vendedor2",
    "email": "<EMAIL>",
    "firstName": "Vendedor",
    "lastName": "Dos",
    "active": true,
    "roles": ["ROLE_VENDEDOR"]
  }
]
```

### Obtener Detalles de un Usuario

**Endpoint:** `GET /api/admin/usuarios/{id}`  
**Acceso:** Solo usuarios con rol `ROLE_ADMIN`  
**Descripción:** Obtiene los detalles de un usuario específico por su ID.

**Request:**
```http
GET /api/admin/usuarios/3 HTTP/1.1
Host: localhost:8080
Authorization: Bearer {jwt_token}
```

**Response (200 OK):**
```json
{
  "id": 3,
  "username": "vendedor1",
  "email": "<EMAIL>",
  "firstName": "Vendedor",
  "lastName": "Uno",
  "active": true,
  "roles": ["ROLE_VENDEDOR"]
}
```

**Response (404 Not Found):**
```json
{
  "message": "Usuario no encontrado con ID: 3"
}
```

### Activar/Desactivar un Usuario

**Endpoint:** `PATCH /api/admin/usuarios/{id}/active`  
**Acceso:** Solo usuarios con rol `ROLE_ADMIN`  
**Descripción:** Activa o desactiva un usuario.

**Request:**
```http
PATCH /api/admin/usuarios/3/active?active=false HTTP/1.1
Host: localhost:8080
Authorization: Bearer {jwt_token}
```

**Response (200 OK):**
```json
{
  "message": "Estado de usuario actualizado correctamente a: inactivo"
}
```

**Response (404 Not Found):**
```json
{
  "message": "Usuario no encontrado con ID: 3"
}
```

## Modelos de Datos

### CreateVendedorRequest

```typescript
interface CreateVendedorRequest {
  username: string;      // Requerido, min 3, max 20 caracteres
  email: string;         // Requerido, formato email válido, max 50 caracteres
  password: string;      // Requerido, min 6, max 40 caracteres
  firstName?: string;    // Opcional, max 50 caracteres
  lastName?: string;     // Opcional, max 50 caracteres
}
```

### UserDetailResponse

```typescript
interface UserDetailResponse {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  active: boolean;
  roles: string[];
}
```

### MessageResponse

```typescript
interface MessageResponse {
  message: string;
}
```

## Implementación en Frontend

### Componentes Sugeridos

1. **VendedorList**: Componente para mostrar la lista de vendedores con opciones para ver detalles, activar/desactivar y crear nuevos vendedores.
2. **VendedorForm**: Formulario para crear o editar vendedores.
3. **VendedorDetail**: Componente para mostrar los detalles de un vendedor específico.
4. **AdminDashboard**: Panel de control para administradores que incluye acceso a la gestión de vendedores.

### Flujo de Trabajo

1. El administrador inicia sesión y accede al panel de administración.
2. En la sección de gestión de usuarios, el administrador puede ver la lista de vendedores.
3. El administrador puede crear un nuevo vendedor mediante un formulario.
4. El administrador puede ver los detalles de un vendedor haciendo clic en su nombre o en un botón de detalles.
5. El administrador puede activar o desactivar un vendedor mediante un botón o interruptor en la lista o en la vista de detalles.

### Ejemplos de Código

#### Servicio de Gestión de Vendedores (Angular)

```typescript
// vendor.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class VendorService {
  private apiUrl = `${environment.apiUrl}/api/admin/usuarios`;

  constructor(private http: HttpClient) { }

  // Obtener todos los vendedores
  getVendors(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/vendedores`);
  }

  // Obtener un vendedor por ID
  getVendorById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${id}`);
  }

  // Crear un nuevo vendedor
  createVendor(vendorData: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/vendedores`, vendorData);
  }

  // Activar/desactivar un vendedor
  setVendorStatus(id: number, active: boolean): Observable<any> {
    return this.http.patch<any>(`${this.apiUrl}/${id}/active?active=${active}`, {});
  }
}
```

#### Componente de Lista de Vendedores (React)

```jsx
// VendorList.jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';

const VendorList = () => {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchVendors = async () => {
      try {
        const token = localStorage.getItem('authToken');
        const response = await axios.get('http://localhost:8080/api/admin/usuarios/vendedores', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        setVendors(response.data);
        setLoading(false);
      } catch (err) {
        setError('Error al cargar los vendedores');
        setLoading(false);
      }
    };

    fetchVendors();
  }, []);

  const handleStatusChange = async (id, currentStatus) => {
    try {
      const token = localStorage.getItem('authToken');
      await axios.patch(`http://localhost:8080/api/admin/usuarios/${id}/active?active=${!currentStatus}`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // Actualizar la lista de vendedores
      setVendors(vendors.map(vendor => 
        vendor.id === id ? { ...vendor, active: !currentStatus } : vendor
      ));
    } catch (err) {
      setError('Error al cambiar el estado del vendedor');
    }
  };

  if (loading) return <div>Cargando...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="vendor-list">
      <h2>Gestión de Vendedores</h2>
      <Link to="/admin/vendors/new" className="btn btn-primary">
        Crear Nuevo Vendedor
      </Link>
      
      <table className="table mt-4">
        <thead>
          <tr>
            <th>ID</th>
            <th>Usuario</th>
            <th>Email</th>
            <th>Nombre</th>
            <th>Apellido</th>
            <th>Estado</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          {vendors.map(vendor => (
            <tr key={vendor.id}>
              <td>{vendor.id}</td>
              <td>{vendor.username}</td>
              <td>{vendor.email}</td>
              <td>{vendor.firstName}</td>
              <td>{vendor.lastName}</td>
              <td>
                <span className={`badge ${vendor.active ? 'bg-success' : 'bg-danger'}`}>
                  {vendor.active ? 'Activo' : 'Inactivo'}
                </span>
              </td>
              <td>
                <Link to={`/admin/vendors/${vendor.id}`} className="btn btn-sm btn-info me-2">
                  Detalles
                </Link>
                <button 
                  className={`btn btn-sm ${vendor.active ? 'btn-warning' : 'btn-success'}`}
                  onClick={() => handleStatusChange(vendor.id, vendor.active)}
                >
                  {vendor.active ? 'Desactivar' : 'Activar'}
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default VendorList;
```

#### Formulario de Creación de Vendedor (Vue.js)

```vue
<!-- VendorForm.vue -->
<template>
  <div class="vendor-form">
    <h2>{{ isEditing ? 'Editar Vendedor' : 'Crear Nuevo Vendedor' }}</h2>
    
    <form @submit.prevent="submitForm">
      <div class="mb-3">
        <label for="username" class="form-label">Nombre de Usuario</label>
        <input 
          type="text" 
          class="form-control" 
          id="username" 
          v-model="vendor.username"
          :disabled="isEditing"
          required
          minlength="3"
          maxlength="20"
        >
        <div v-if="errors.username" class="text-danger">{{ errors.username }}</div>
      </div>
      
      <div class="mb-3">
        <label for="email" class="form-label">Email</label>
        <input 
          type="email" 
          class="form-control" 
          id="email" 
          v-model="vendor.email"
          :disabled="isEditing"
          required
          maxlength="50"
        >
        <div v-if="errors.email" class="text-danger">{{ errors.email }}</div>
      </div>
      
      <div class="mb-3" v-if="!isEditing">
        <label for="password" class="form-label">Contraseña</label>
        <input 
          type="password" 
          class="form-control" 
          id="password" 
          v-model="vendor.password"
          required
          minlength="6"
          maxlength="40"
        >
        <div v-if="errors.password" class="text-danger">{{ errors.password }}</div>
      </div>
      
      <div class="mb-3">
        <label for="firstName" class="form-label">Nombre</label>
        <input 
          type="text" 
          class="form-control" 
          id="firstName" 
          v-model="vendor.firstName"
          maxlength="50"
        >
      </div>
      
      <div class="mb-3">
        <label for="lastName" class="form-label">Apellido</label>
        <input 
          type="text" 
          class="form-control" 
          id="lastName" 
          v-model="vendor.lastName"
          maxlength="50"
        >
      </div>
      
      <button type="submit" class="btn btn-primary">
        {{ isEditing ? 'Actualizar' : 'Crear' }} Vendedor
      </button>
      <router-link to="/admin/vendors" class="btn btn-secondary ms-2">
        Cancelar
      </router-link>
    </form>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'VendorForm',
  props: {
    vendorId: {
      type: Number,
      required: false
    }
  },
  data() {
    return {
      vendor: {
        username: '',
        email: '',
        password: '',
        firstName: '',
        lastName: ''
      },
      errors: {},
      isEditing: false
    };
  },
  created() {
    if (this.vendorId) {
      this.isEditing = true;
      this.fetchVendor();
    }
  },
  methods: {
    async fetchVendor() {
      try {
        const token = localStorage.getItem('authToken');
        const response = await axios.get(`http://localhost:8080/api/admin/usuarios/${this.vendorId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        // Excluir la contraseña y asignar los datos
        const { password, ...vendorData } = response.data;
        this.vendor = vendorData;
      } catch (error) {
        console.error('Error al obtener datos del vendedor', error);
      }
    },
    async submitForm() {
      try {
        this.errors = {};
        const token = localStorage.getItem('authToken');
        
        if (!this.isEditing) {
          // Crear nuevo vendedor
          await axios.post('http://localhost:8080/api/admin/usuarios/vendedores', this.vendor, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          this.$router.push('/admin/vendors');
        } else {
          // La API actual no soporta actualización directa de vendedores
          // Esta funcionalidad podría implementarse en el futuro
          alert('La actualización de vendedores no está implementada en la API actual');
        }
      } catch (error) {
        if (error.response && error.response.data) {
          this.errors = error.response.data;
        } else {
          console.error('Error al enviar el formulario', error);
        }
      }
    }
  }
};
</script>
```

## Consideraciones de Seguridad

1. **Autenticación y Autorización**:
   - Todas las peticiones a los endpoints de administración deben incluir un token JWT válido.
   - Solo los usuarios con rol `ROLE_ADMIN` pueden acceder a estos endpoints.
   - Implementar verificaciones de rol en el frontend para ocultar opciones no autorizadas.

2. **Validación de Datos**:
   - Validar todos los datos de entrada en el cliente antes de enviarlos al servidor.
   - Implementar mensajes de error claros para guiar al usuario.

3. **Manejo de Tokens**:
   - Almacenar el token JWT de forma segura (por ejemplo, en localStorage o en una cookie HttpOnly).
   - Implementar un mecanismo para renovar el token cuando esté próximo a expirar.
   - Eliminar el token al cerrar sesión.

4. **Protección contra CSRF**:
   - Aunque la API tiene CSRF deshabilitado, es una buena práctica implementar protección CSRF en el frontend.

## Pruebas

### Pruebas Manuales

1. **Crear un Vendedor**:
   - Iniciar sesión como administrador.
   - Navegar a la sección de gestión de vendedores.
   - Crear un nuevo vendedor con datos válidos.
   - Verificar que el vendedor aparece en la lista de vendedores.

2. **Activar/Desactivar un Vendedor**:
   - Seleccionar un vendedor de la lista.
   - Cambiar su estado (activar/desactivar).
   - Verificar que el estado se actualiza correctamente.
   - Intentar iniciar sesión con las credenciales del vendedor desactivado (debería fallar).

3. **Validación de Datos**:
   - Intentar crear un vendedor con datos inválidos (por ejemplo, un email mal formado).
   - Verificar que se muestran mensajes de error apropiados.
   - Intentar crear un vendedor con un nombre de usuario o email ya existente.
   - Verificar que se muestra un mensaje de error indicando que el usuario/email ya está en uso.

### Pruebas Automatizadas

Se recomienda implementar pruebas automatizadas para esta funcionalidad:

1. **Pruebas Unitarias** para los componentes y servicios del frontend.
2. **Pruebas de Integración** para verificar la interacción entre componentes.
3. **Pruebas E2E** para simular el flujo completo de creación y gestión de vendedores.

---

Este documento proporciona una guía completa para implementar la funcionalidad de gestión de vendedores en el frontend de la plataforma de ecommerce. Si tienes alguna pregunta o necesitas aclaraciones adicionales, no dudes en contactar al equipo de desarrollo.