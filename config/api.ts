/**
 * Configuración de la API para el proyecto de ecommerce
 */

// Configuración base de la API
export const API_CONFIG = {
  // En desarrollo, usamos la URL del servidor de desarrollo
  // En producción, usaríamos la URL del servidor de producción
  BASE_URL: 'http://localhost:8080',
  
  // Endpoints de la API
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/api/auth/login',
      SIGNUP: '/api/auth/signup',
      SESSION_INFO: '/api/auth/session-info',
      LOGOUT: '/api/auth/logout',
    },
    // Aquí se pueden agregar más endpoints en el futuro
    PRODUCTS: {
      LIST: '/api/products',
      DETAIL: '/api/products',
    },
    USERS: {
      PROFILE: '/api/usuarios/profile',
    },
    ADMIN: {
      VENDORS: {
        LIST: '/api/admin/usuarios/vendedores',
        DETAIL: '/api/admin/usuarios',
        STATUS: '/api/admin/usuarios',
      }
    }
  },
  TIMEOUT: 10000, // 10 segundos
};

// Headers por defecto
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Función para obtener headers con autorización
export const getAuthHeaders = (token?: string | null) => {
  const headers = { ...DEFAULT_HEADERS };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

// Función para construir URL completa
export const buildUrl = (endpoint: string) => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Configuración para diferentes entornos
export const getApiConfig = () => {
  // En React Native, podemos usar diferentes URLs según el entorno
  const isDevelopment = __DEV__;

  if (isDevelopment) {
    // Detectar si estamos en web o móvil
    const isWeb = typeof window !== 'undefined';

    return {
      ...API_CONFIG,
      // En web, usar proxy para evitar CORS temporalmente
      BASE_URL: isWeb ? '/api-proxy' : 'http://localhost:8080',
    };
  }

  return {
    ...API_CONFIG,
    BASE_URL: 'https://your-production-api.com', // Para producción
  };
};
