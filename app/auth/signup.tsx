/**
 * Pantalla de Registro
 */

import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import SignupForm from '../../components/auth/SignupForm';

export default function SignupScreen() {
  return (
    <SafeAreaView className="flex-1">
      <SignupForm 
        onSwitchToLogin={() => router.push('/auth/login')}
      />
    </SafeAreaView>
  );
}
