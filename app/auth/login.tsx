/**
 * <PERSON><PERSON><PERSON> de Login
 */

import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import LoginForm from '../../components/auth/LoginForm';

export default function LoginScreen() {
  return (
    <SafeAreaView className="flex-1">
      <LoginForm 
        onSwitchToSignup={() => router.push('/auth/signup')}
      />
    </SafeAreaView>
  );
}
