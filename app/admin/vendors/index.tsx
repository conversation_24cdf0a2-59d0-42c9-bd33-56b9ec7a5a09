/**
 * Página de listado de vendedores
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator, 
  StyleSheet,
  Alert
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../contexts/AuthContext';
import { UserDetailResponse } from '../../../types/auth';
import vendorService from '../../../services/vendorService';
import { FluentColors } from '../../../constants/WindowsPhoneColors';

export default function VendorListScreen() {
  const { token } = useAuth();
  const [vendors, setVendors] = useState<UserDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Cargar vendedores
  const loadVendors = async () => {
    if (!token) {
      console.log('⚠️ No hay token disponible, redirigiendo a login...');
      setError('Sesión no iniciada. Por favor, inicie sesión.');
      router.replace('/auth');
      return;
    }
    
    try {
      console.log('🔄 Iniciando carga de vendedores...');
      setLoading(true);
      setError(null);
      
      console.log('📡 Llamando al servicio de vendedores...');
      const data = await vendorService.getVendors(token);
      
      console.log(`✅ Vendedores cargados: ${data.length}`);
      setVendors(data);
    } catch (err: any) {
      console.error('❌ Error al cargar vendedores:', err);
      
      // Manejar errores específicos
      if (err.status === 401) {
        console.log('⚠️ Error de autenticación, redirigiendo a login...');
        setError('Sesión expirada. Por favor, inicie sesión nuevamente.');
        router.replace('/auth');
      } else if (err.code === 'CORS_ERROR') {
        setError('Error de conexión con el servidor. Problema de CORS detectado.');
      } else {
        setError(err.message || 'Error al cargar los vendedores');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Cargar al montar el componente
  useEffect(() => {
    console.log('🔄 Componente montado, token disponible:', !!token);
    loadVendors();
  }, [token]);

  // Manejar cambio de estado (activar/desactivar)
  const handleStatusChange = async (id: number, currentStatus: boolean) => {
    if (!token) return;
    
    try {
      await vendorService.setVendorStatus(id, !currentStatus, token);
      
      // Actualizar la lista de vendedores
      setVendors(vendors.map(vendor => 
        vendor.id === id ? { ...vendor, active: !currentStatus } : vendor
      ));
      
      // Mostrar mensaje de éxito
      Alert.alert(
        'Estado actualizado',
        `El vendedor ha sido ${!currentStatus ? 'activado' : 'desactivado'} correctamente.`
      );
    } catch (err: any) {
      Alert.alert('Error', err.message || 'Error al cambiar el estado del vendedor');
    }
  };

  // Renderizar cada item de la lista
  const renderVendorItem = ({ item }: { item: UserDetailResponse }) => {
    return (
      <View style={styles.vendorCard}>
        <View style={styles.vendorHeader}>
          <View style={styles.vendorIcon}>
            <FluentPerson size={24} color={FluentColors.primary} />
          </View>
          <View style={styles.vendorInfo}>
            <Text style={styles.vendorUsername}>@{item.username}</Text>
            <Text style={styles.vendorEmail}>{item.email}</Text>
          </View>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: item.active ? '#107c1020' : '#d1343820' }
          ]}>
            <Text style={[
              styles.statusText, 
              { color: item.active ? '#107c10' : '#d13438' }
            ]}>
              {item.active ? 'Activo' : 'Inactivo'}
            </Text>
          </View>
        </View>
        
        <View style={styles.vendorName}>
          <Text style={styles.vendorFullName}>
            {item.firstName || ''} {item.lastName || ''}
          </Text>
        </View>
        
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push(`/admin/vendors/${item.id}` as any)}
          >
            <FluentInfo size={16} color={FluentColors.primary} />
            <Text style={styles.actionText}>Detalles</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleStatusChange(item.id, item.active)}
          >
            {item.active ? (
              <>
                <FluentDismiss size={16} color="#d13438" />
                <Text style={[styles.actionText, { color: '#d13438' }]}>Desactivar</Text>
              </>
            ) : (
              <>
                <FluentCheckmark size={16} color="#107c10" />
                <Text style={[styles.actionText, { color: '#107c10' }]}>Activar</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Gestión de Vendedores</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/admin/vendors/new' as any)}
          >
            <Text style={styles.addButtonIcon}>➕</Text>
            <Text style={styles.addButtonText}>Nuevo Vendedor</Text>
          </TouchableOpacity>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorIcon}>⚠️</Text>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity 
              style={styles.errorButton}
              onPress={() => {
                setError(null);
                loadVendors();
              }}
            >
              <Text style={styles.errorButtonText}>Reintentar</Text>
            </TouchableOpacity>
          </View>
        )}

        {loading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={FluentColors.primary} />
            <Text style={styles.loadingText}>Cargando vendedores...</Text>
          </View>
        ) : (
          <FlatList
            data={vendors}
            renderItem={renderVendorItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              loadVendors();
            }}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  No hay vendedores registrados.
                </Text>
              </View>
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#323130',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: FluentColors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  addButtonIcon: {
    fontSize: 16,
    color: 'white',
    marginRight: 4,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: '#FFF4CE',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FFB900',
    alignItems: 'center',
  },
  errorIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#323130',
    textAlign: 'center',
    marginBottom: 12,
    fontFamily: 'IBMPlexSans-Regular',
  },
  errorButton: {
    backgroundColor: '#0078D4',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  errorButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#323130',
    fontFamily: 'IBMPlexSans-Regular',
  },
  listContainer: {
    padding: 16,
  },
  vendorCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  vendorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  vendorIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${FluentColors.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  vendorInfo: {
    flex: 1,
  },
  vendorUsername: {
    fontSize: 16,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#323130',
  },
  vendorEmail: {
    fontSize: 14,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
  },
  vendorName: {
    marginBottom: 12,
  },
  vendorFullName: {
    fontSize: 14,
    color: '#323130',
    fontFamily: 'IBMPlexSans-Regular',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Medium',
  },
  actionsContainer: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
    marginTop: 4,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    paddingVertical: 4,
  },
  actionText: {
    marginLeft: 4,
    fontSize: 14,
    color: FluentColors.primary,
    fontFamily: 'IBMPlexSans-Medium',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
  },
  errorContainer: {
    backgroundColor: '#FDE7E9',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#D13438',
  },
  errorText: {
    color: '#D13438',
    fontFamily: 'IBMPlexSans-Regular',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#605e5c',
    textAlign: 'center',
    fontFamily: 'IBMPlexSans-Regular',
  },
});