/**
 * <PERSON><PERSON><PERSON><PERSON> de detalles de un vendedor
 */

import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  ActivityIndicator, 
  StyleSheet,
  Alert,
  TouchableOpacity
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../contexts/AuthContext';
import { UserDetailResponse, getRoleDisplayName, getRoleColor } from '../../../types/auth';
import vendorService from '../../../services/vendorService';
import { 
  FluentPerson, 
  FluentMail, 
  FluentCheckmark, 
  FluentDismiss,
  FluentArrowLeft,
  FluentColors
} from '../../../components/FluentIconComponent';
import { FluentPrimaryButton, FluentSecondaryButton } from '../../../components/ui';

export default function VendorDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { token } = useAuth();
  const [vendor, setVendor] = useState<UserDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Cargar detalles del vendedor
  const loadVendorDetails = async () => {
    if (!token || !id) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await vendorService.getVendorById(parseInt(id), token);
      setVendor(data);
    } catch (err: any) {
      setError(err.message || 'Error al cargar los detalles del vendedor');
    } finally {
      setLoading(false);
    }
  };

  // Cargar al montar el componente
  useEffect(() => {
    loadVendorDetails();
  }, [id, token]);

  // Manejar cambio de estado (activar/desactivar)
  const handleStatusChange = async () => {
    if (!token || !vendor) return;
    
    try {
      await vendorService.setVendorStatus(vendor.id, !vendor.active, token);
      
      // Actualizar el estado del vendedor
      setVendor(prev => prev ? { ...prev, active: !prev.active } : null);
      
      // Mostrar mensaje de éxito
      Alert.alert(
        'Estado actualizado',
        `El vendedor ha sido ${!vendor.active ? 'activado' : 'desactivado'} correctamente.`
      );
    } catch (err: any) {
      Alert.alert('Error', err.message || 'Error al cambiar el estado del vendedor');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={FluentColors.primary} />
          <Text style={styles.loadingText}>Cargando detalles...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Error</Text>
          <Text style={styles.errorText}>{error}</Text>
          <FluentPrimaryButton
            title="Volver"
            onPress={() => router.back()}
            size="medium"
            style={{ marginTop: 16 }}
          />
        </View>
      </SafeAreaView>
    );
  }

  if (!vendor) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Vendedor no encontrado</Text>
          <FluentPrimaryButton
            title="Volver"
            onPress={() => router.back()}
            size="medium"
            style={{ marginTop: 16 }}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView style={styles.container}>
        {/* Header con botón de volver */}
        <View style={styles.headerNav}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <FluentArrowLeft size={20} color={FluentColors.primary} />
            <Text style={styles.backText}>Volver</Text>
          </TouchableOpacity>
        </View>

        {/* Perfil del vendedor */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <FluentPerson size={40} color={FluentColors.primary} />
          </View>
          <View style={styles.profileInfo}>
            <Text style={styles.username}>@{vendor.username}</Text>
            <View style={styles.rolesContainer}>
              {vendor.roles.map((role, index) => (
                <View
                  key={index}
                  style={[
                    styles.roleBadge,
                    { backgroundColor: `${getRoleColor(role)}20` }
                  ]}
                >
                  <Text
                    style={[
                      styles.roleText,
                      { color: getRoleColor(role) }
                    ]}
                  >
                    {getRoleDisplayName(role)}
                  </Text>
                </View>
              ))}
            </View>
          </View>
          <View style={[
            styles.statusBadge,
            { backgroundColor: vendor.active ? '#107c1020' : '#d1343820' }
          ]}>
            <Text style={[
              styles.statusText,
              { color: vendor.active ? '#107c10' : '#d13438' }
            ]}>
              {vendor.active ? 'Activo' : 'Inactivo'}
            </Text>
          </View>
        </View>

        {/* Detalles del vendedor */}
        <View style={styles.detailsContainer}>
          <Text style={styles.sectionTitle}>Información Personal</Text>
          
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Nombre completo</Text>
            <Text style={styles.detailValue}>
              {vendor.firstName || ''} {vendor.lastName || ''}
            </Text>
          </View>
          
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Email</Text>
            <View style={styles.detailWithIcon}>
              <FluentMail size={16} color={FluentColors.neutral.gray120} />
              <Text style={styles.detailValue}>{vendor.email}</Text>
            </View>
          </View>
          
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>ID de usuario</Text>
            <Text style={styles.detailValue}>{vendor.id}</Text>
          </View>
        </View>

        {/* Acciones */}
        <View style={styles.actionsContainer}>
          <FluentSecondaryButton
            title={vendor.active ? 'Desactivar Vendedor' : 'Activar Vendedor'}
            onPress={handleStatusChange}
            size="medium"
            icon={vendor.active ? 
              <FluentDismiss size={16} color="#d13438" /> : 
              <FluentCheckmark size={16} color="#107c10" />
            }
            style={{ 
              backgroundColor: vendor.active ? '#FDE7E9' : '#E6F2D9',
              borderColor: vendor.active ? '#D13438' : '#107c10',
            }}
            textStyle={{ 
              color: vendor.active ? '#D13438' : '#107c10' 
            }}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerNav: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backText: {
    marginLeft: 4,
    fontSize: 16,
    color: FluentColors.primary,
    fontFamily: 'IBMPlexSans-Medium',
  },
  profileHeader: {
    backgroundColor: 'white',
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  avatarContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: `${FluentColors.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  username: {
    fontSize: 18,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#323130',
    marginBottom: 4,
  },
  rolesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 4,
  },
  roleText: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Medium',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Medium',
  },
  detailsContainer: {
    backgroundColor: 'white',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#323130',
    marginBottom: 16,
  },
  detailItem: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 14,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#323130',
    fontFamily: 'IBMPlexSans-Medium',
  },
  detailWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionsContainer: {
    padding: 16,
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#d13438',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#605e5c',
    textAlign: 'center',
    fontFamily: 'IBMPlexSans-Regular',
  },
});