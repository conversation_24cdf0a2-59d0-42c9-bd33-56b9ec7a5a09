/**
 * Página para crear un nuevo vendedor
 */

import React, { useState } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  KeyboardAvoidingView, 
  Platform, 
  StyleSheet,
  Alert
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../contexts/AuthContext';
import { CreateVendedorRequest, FormErrors } from '../../../types/auth';
import vendorService from '../../../services/vendorService';
import { 
  FluentPerson, 
  FluentMail, 
  FluentLock, 
  FluentText,
  FluentColors
} from '../../../components/FluentIconComponent';
import { 
  FluentPrimaryButton, 
  FluentSecondaryButton,
  FluentTextInput,
  FluentPasswordInput
} from '../../../components/ui';

export default function CreateVendorScreen() {
  const { token } = useAuth();
  const [loading, setLoading] = useState(false);
  
  // Estado del formulario
  const [formData, setFormData] = useState<CreateVendedorRequest>({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
  });
  
  // Estado de errores
  const [errors, setErrors] = useState<FormErrors>({});

  // Validar formulario
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    // Validar username
    if (!formData.username.trim()) {
      newErrors.username = 'El nombre de usuario es requerido';
    } else if (formData.username.length < 3) {
      newErrors.username = 'El nombre de usuario debe tener al menos 3 caracteres';
    } else if (formData.username.length > 20) {
      newErrors.username = 'El nombre de usuario no debe exceder 20 caracteres';
    }
    
    // Validar email
    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'El email no es válido';
    } else if (formData.email.length > 50) {
      newErrors.email = 'El email no debe exceder 50 caracteres';
    }
    
    // Validar password
    if (!formData.password) {
      newErrors.password = 'La contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    } else if (formData.password.length > 40) {
      newErrors.password = 'La contraseña no debe exceder 40 caracteres';
    }
    
    // Validar firstName (opcional)
    if (formData.firstName && formData.firstName.length > 50) {
      newErrors.firstName = 'El nombre no debe exceder 50 caracteres';
    }
    
    // Validar lastName (opcional)
    if (formData.lastName && formData.lastName.length > 50) {
      newErrors.lastName = 'El apellido no debe exceder 50 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar cambios en los campos
  const handleChange = (field: keyof CreateVendedorRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Enviar formulario
  const handleSubmit = async () => {
    if (!token) return;
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      await vendorService.createVendor(formData, token);
      
      Alert.alert(
        'Vendedor creado',
        'El vendedor ha sido creado exitosamente',
        [
          { 
            text: 'OK', 
            onPress: () => router.push('/admin/vendors' as any) 
          }
        ]
      );
    } catch (err: any) {
      // Manejar errores específicos
      if (err.status === 400) {
        // Posible error de validación o usuario duplicado
        if (err.message.includes('username')) {
          setErrors(prev => ({ ...prev, username: 'El nombre de usuario ya está en uso' }));
        } else if (err.message.includes('email')) {
          setErrors(prev => ({ ...prev, email: 'El email ya está en uso' }));
        } else {
          Alert.alert('Error', err.message || 'Error al crear el vendedor');
        }
      } else {
        Alert.alert('Error', err.message || 'Error al crear el vendedor');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView 
          contentContainerStyle={styles.container}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text style={styles.title}>Crear Nuevo Vendedor</Text>
            <Text style={styles.subtitle}>
              Complete el formulario para crear un nuevo usuario con rol de vendedor
            </Text>
          </View>

          <View style={styles.formContainer}>
            {/* Username */}
            <FluentTextInput
              label="Nombre de Usuario"
              placeholder="Ingrese el nombre de usuario"
              value={formData.username}
              onChangeText={(text) => handleChange('username', text)}
              leftIcon={<FluentPerson size={20} color={FluentColors.neutral.gray120} />}
              error={errors.username}
              disabled={loading}
              autoCapitalize="none"
              required
            />

            {/* Email */}
            <FluentTextInput
              label="Email"
              placeholder="Ingrese el email"
              value={formData.email}
              onChangeText={(text) => handleChange('email', text)}
              leftIcon={<FluentMail size={20} color={FluentColors.neutral.gray120} />}
              error={errors.email}
              disabled={loading}
              autoCapitalize="none"
              keyboardType="email-address"
              required
            />

            {/* Password */}
            <FluentPasswordInput
              label="Contraseña"
              placeholder="Ingrese la contraseña"
              value={formData.password}
              onChangeText={(text) => handleChange('password', text)}
              leftIcon={<FluentLock size={20} color={FluentColors.neutral.gray120} />}
              error={errors.password}
              disabled={loading}
              required
            />

            {/* FirstName */}
            <FluentTextInput
              label="Nombre"
              placeholder="Ingrese el nombre (opcional)"
              value={formData.firstName}
              onChangeText={(text) => handleChange('firstName', text)}
              leftIcon={<FluentText size={20} color={FluentColors.neutral.gray120} />}
              error={errors.firstName}
              disabled={loading}
            />

            {/* LastName */}
            <FluentTextInput
              label="Apellido"
              placeholder="Ingrese el apellido (opcional)"
              value={formData.lastName}
              onChangeText={(text) => handleChange('lastName', text)}
              leftIcon={<FluentText size={20} color={FluentColors.neutral.gray120} />}
              error={errors.lastName}
              disabled={loading}
            />
          </View>

          <View style={styles.actionsContainer}>
            <FluentSecondaryButton
              title="Cancelar"
              onPress={() => router.back()}
              disabled={loading}
              size="medium"
              style={{ marginRight: 8 }}
            />
            <FluentPrimaryButton
              title={loading ? 'Creando...' : 'Crear Vendedor'}
              onPress={handleSubmit}
              disabled={loading}
              loading={loading}
              size="medium"
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#323130',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 24,
  },
});