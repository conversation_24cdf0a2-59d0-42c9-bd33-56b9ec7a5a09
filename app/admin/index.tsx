/**
 * Página principal del panel de administración
 */

import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FluentColors } from '@/constants/WindowsPhoneColors';

export default function AdminDashboard() {
  // Opciones del panel de administración
  const adminOptions = [
    {
      title: 'Gestión de Vendedores',
      description: 'Crear y administrar usuarios con rol de vendedor',
      iconText: '👤',
      route: '/admin/vendors',
      color: FluentColors.primary,
    },
    {
      title: 'Gestión de Productos',
      description: 'Administrar el catálogo de productos',
      iconText: '🛍️',
      route: '/admin/products',
      color: '#107c10', // Verde
    },
    {
      title: 'Gestión de Pedidos',
      description: 'Ver y administrar los pedidos realizados',
      iconText: '📦',
      route: '/admin/orders',
      color: '#d13438', // Rojo
    },
    {
      title: 'Configuración',
      description: 'Configuración general de la plataforma',
      iconText: '⚙️',
      route: '/admin/settings',
      color: '#5c2d91', // Púrpura
    },
  ];

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <View style={styles.header}>
          <Text style={styles.title}>Panel de Administración</Text>
          <Text style={styles.subtitle}>
            Bienvenido al panel de administración. Seleccione una opción para comenzar.
          </Text>
        </View>

        <View style={styles.optionsGrid}>
          {adminOptions.map((option, index) => {
            return (
              <TouchableOpacity
                key={index}
                style={styles.optionCard}
                onPress={() => router.push(option.route as any)}
                activeOpacity={0.7}
              >
                <View style={[styles.iconContainer, { backgroundColor: `${option.color}10` }]}>
                  <Text style={{ fontSize: 24 }}>{option.iconText}</Text>
                </View>
                <Text style={styles.optionTitle}>{option.title}</Text>
                <Text style={styles.optionDescription}>{option.description}</Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'IBMPlexSans-Bold',
    color: '#323130',
  },
  subtitle: {
    fontSize: 16,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  optionCard: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: '#323130',
  },
  optionDescription: {
    fontSize: 12,
    color: '#605e5c',
    fontFamily: 'IBMPlexSans-Regular',
  },
});