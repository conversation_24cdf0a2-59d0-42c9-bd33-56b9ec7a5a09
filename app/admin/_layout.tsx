/**
 * Layout para la sección de administración
 */

import React from 'react';
import { Stack } from 'expo-router';
import AdminProtectedRoute from '../../components/auth/AdminProtectedRoute';

export default function AdminLayout() {
  return (
    <AdminProtectedRoute>
      <Stack>
        <Stack.Screen 
          name="index" 
          options={{ 
            title: 'Panel de Administración',
            headerShown: true,
          }} 
        />
        <Stack.Screen 
          name="vendors/index" 
          options={{ 
            title: 'Gestión de Vendedores',
            headerShown: true,
          }} 
        />
        <Stack.Screen 
          name="vendors/new" 
          options={{ 
            title: 'Nuevo Vendedor',
            headerShown: true,
          }} 
        />
        <Stack.Screen 
          name="vendors/[id]" 
          options={{ 
            title: 'Detalles de Vendedor',
            headerShown: true,
          }} 
        />
      </Stack>
    </AdminProtectedRoute>
  );
}