import { Image } from 'expo-image';
import { Platform, StyleSheet, View, Text } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/contexts/AuthContext';
import { getRoleDisplayName, getRoleColor } from '@/types/auth';
import { FluentPerson, FluentColors } from '@/components/FluentIconComponent';

export default function HomeScreen() {
  const { user } = useAuth();

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">
          ¡Bienvenido{user?.firstName ? `, ${user.firstName}` : ''}!
        </ThemedText>
        <HelloWave />
      </ThemedView>

      {/* Información del usuario */}
      {user && (
        <ThemedView style={styles.stepContainer}>
          <ThemedText type="subtitle">Tu Cuenta</ThemedText>
          <View className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
            <View className="flex-row items-center mb-3">
              <View
                className="w-12 h-12 rounded-full items-center justify-center mr-3"
                style={{ backgroundColor: `${getRoleColor(user.roles[0] || '')}20` }}
              >
                <FluentPerson size={24} color={getRoleColor(user.roles[0] || '')} />
              </View>
              <View className="flex-1">
                <Text className="text-lg font-semibold text-gray-900 dark:text-white">
                  @{user.username}
                </Text>
                <Text className="text-gray-600 dark:text-gray-300">
                  {user.email}
                </Text>
              </View>
            </View>
            <View className="flex-row flex-wrap">
              {user.roles.map((role, index) => (
                <View
                  key={index}
                  className="px-2 py-1 rounded mr-2 mb-1"
                  style={{ backgroundColor: `${getRoleColor(role)}20` }}
                >
                  <Text
                    className="text-xs font-medium"
                    style={{ color: getRoleColor(role) }}
                  >
                    {getRoleDisplayName(role)}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </ThemedView>
      )}

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Ecommerce App</ThemedText>
        <ThemedText>
          Esta es tu aplicación de ecommerce con autenticación JWT integrada.
          Puedes navegar por las diferentes secciones usando las pestañas de abajo.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Funcionalidades</ThemedText>
        <ThemedText>
          • Autenticación segura con JWT{'\n'}
          • Gestión de perfiles de usuario{'\n'}
          • Sistema de roles (Admin, Vendedor, Usuario){'\n'}
          • Interfaz moderna con Tailwind CSS{'\n'}
          • Iconos de Fluent UI
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Próximas funcionalidades</ThemedText>
        <ThemedText>
          • Catálogo de productos{'\n'}
          • Carrito de compras{'\n'}
          • Gestión de pedidos{'\n'}
          • Panel de administración
        </ThemedText>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
});
