import { Tabs } from 'expo-router';
import React from 'react';

import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Windows10NavBar from '@/components/navigation/Windows10NavBar';

export default function TabLayout() {
  return (
    <ProtectedRoute>
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: { display: 'none' }, // Ocultar la tab bar por defecto
        }}
        tabBar={() => <Windows10NavBar />} // Usar solo la barra Windows 10
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Inicio',
          }}
        />
        <Tabs.Screen
          name="explore"
          options={{
            title: 'Configuración',
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Perfil',
          }}
        />
      </Tabs>
    </ProtectedRoute>
  );
}
