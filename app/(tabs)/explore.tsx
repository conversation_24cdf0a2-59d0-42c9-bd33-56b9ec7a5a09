import { StyleSheet } from 'react-native';

import { Collapsible } from '@/components/Collapsible';
import { ExternalLink } from '@/components/ExternalLink';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import WindowsPhoneColorDemo from '@/components/WindowsPhoneColorDemo';
import WindowsPhoneComponentsDemo from '@/components/WindowsPhoneComponentsDemo';
import WindowsPhoneTestDemo from '@/components/WindowsPhoneTestDemo';


export default function TabTwoScreen() {
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#808080"
          name="gear"
          style={styles.headerImage}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">Configuración</ThemedText>
      </ThemedView>
      <ThemedText>Información sobre la aplicación Commerce y su configuración.</ThemedText>

      <Collapsible title="Sistema de Colores Windows Phone UI">
        <ThemedText>
          Sistema de colores expandido basado en Windows Phone UI con soporte
          para modo claro/oscuro y tokens de diseño en Tailwind CSS.
        </ThemedText>
        <WindowsPhoneColorDemo />
      </Collapsible>

      <Collapsible title="Componentes Windows Phone UI">
        <ThemedText>
          Componentes básicos rediseñados con estilo Windows Phone UI.
          Botones minimalistas, inputs limpios y cards estilo tiles.
        </ThemedText>
        <WindowsPhoneComponentsDemo />
      </Collapsible>

      <Collapsible title="Test Windows Phone Components">
        <ThemedText>
          Prueba simple de componentes Windows Phone para verificar importaciones.
        </ThemedText>
        <WindowsPhoneTestDemo />
      </Collapsible>

      <Collapsible title="Información de la App">
        <ThemedText>
          Commerce App - Aplicación de ecommerce desarrollada con React Native y Expo.
        </ThemedText>
        <ThemedText>
          Características principales:
        </ThemedText>
        <ThemedText>• Sistema de autenticación completo</ThemedText>
        <ThemedText>• Diseño basado en Fluent UI</ThemedText>
        <ThemedText>• Tipografía IBM Plex Sans Light</ThemedText>
        <ThemedText>• Integración con API Spring Boot</ThemedText>
      </Collapsible>

      <Collapsible title="Tecnologías Utilizadas">
        <ThemedText>
          <ThemedText type="defaultSemiBold">Frontend:</ThemedText> React Native, Expo, TypeScript
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">Estilos:</ThemedText> NativeWind (Tailwind CSS), Fluent UI Design
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">Tipografía:</ThemedText> IBM Plex Sans (Light 300 por defecto)
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">Backend:</ThemedText> Spring Boot API (localhost:8080)
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">Estado:</ThemedText> Context API con useReducer
        </ThemedText>
      </Collapsible>

      <Collapsible title="Componentes Implementados">
        <ThemedText>
          <ThemedText type="defaultSemiBold">Autenticación:</ThemedText>
        </ThemedText>
        <ThemedText>• LoginForm - Formulario de inicio de sesión</ThemedText>
        <ThemedText>• SignupForm - Formulario de registro</ThemedText>
        <ThemedText>• UserProfile - Perfil de usuario con logout directo</ThemedText>
        <ThemedText>• ProtectedRoute - Protección de rutas</ThemedText>

        <ThemedText style={{ marginTop: 16 }}>
          <ThemedText type="defaultSemiBold">UI Components:</ThemedText>
        </ThemedText>
        <ThemedText>• FluentButton - Sistema de botones Fluent UI</ThemedText>
        <ThemedText>• FluentTextInput - Inputs con diseño Fluent UI</ThemedText>
        <ThemedText>• FluentIconComponent - Iconos con estilo Fluent UI</ThemedText>
      </Collapsible>

      <Collapsible title="Configuración de Desarrollo">
        <ThemedText>
          Para ejecutar la aplicación en diferentes plataformas:
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">Web:</ThemedText> Presiona 'w' en la terminal
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">iOS:</ThemedText> Presiona 'i' (requiere Xcode)
        </ThemedText>
        <ThemedText>
          <ThemedText type="defaultSemiBold">Android:</ThemedText> Presiona 'a' (requiere Android Studio)
        </ThemedText>
        <ExternalLink href="https://docs.expo.dev/get-started/installation/">
          <ThemedText type="link">Documentación de Expo</ThemedText>
        </ExternalLink>
      </Collapsible>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#808080',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});
