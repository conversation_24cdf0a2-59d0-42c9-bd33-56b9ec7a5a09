import React from 'react';
import { View, Text, StyleSheet, StatusBar, SafeAreaView } from 'react-native';
import { WindowsPhoneColors, getThemeColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale, ComponentSpacing } from '../../constants/Spacing';

// Tipos de layout
export type WindowsPhoneLayoutVariant = 'default' | 'accent' | 'dark' | 'fullscreen';

// Props del layout principal
export interface WindowsPhoneLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  variant?: WindowsPhoneLayoutVariant;
  backgroundColor?: string;
  headerContent?: React.ReactNode;
  footerContent?: React.ReactNode;
  isDark?: boolean;
  fullScreen?: boolean;
  statusBarStyle?: 'light-content' | 'dark-content';
  style?: any;
}

export const WindowsPhoneLayout: React.FC<WindowsPhoneLayoutProps> = ({
  children,
  title,
  subtitle,
  backgroundColor,
  headerContent,
  footerContent,
  isDark = false,
  fullScreen = false,
  statusBarStyle = 'dark-content',
  style,
}) => {
  const theme = getThemeColors(isDark);
  
  const layoutStyle = {
    flex: 1,
    backgroundColor: backgroundColor || theme.background.primary,
  };

  const headerStyle = {
    paddingHorizontal: SpacingScale.lg,
    paddingTop: fullScreen ? SpacingScale.md : SpacingScale.xl,
    paddingBottom: SpacingScale.lg,
    backgroundColor: backgroundColor || theme.background.primary,
  };

  const titleStyle = {
    fontSize: 32,
    fontFamily: 'IBMPlexSans-Light',
    color: theme.text.primary,
    marginBottom: subtitle ? SpacingScale.xs : 0,
  };

  const subtitleStyle = {
    fontSize: 16,
    fontFamily: 'IBMPlexSans-Light',
    color: theme.text.secondary,
    opacity: 0.8,
  };

  const contentStyle = {
    flex: 1,
    paddingHorizontal: fullScreen ? 0 : SpacingScale.lg,
  };

  const footerStyle = {
    paddingHorizontal: SpacingScale.lg,
    paddingVertical: SpacingScale.md,
    backgroundColor: backgroundColor || theme.background.primary,
  };

  return (
    <SafeAreaView style={[layoutStyle, style]}>
      <StatusBar 
        barStyle={statusBarStyle}
        backgroundColor={backgroundColor || theme.background.primary}
      />
      
      {/* Header */}
      {(title || subtitle || headerContent) && (
        <View style={headerStyle}>
          {title && (
            <Text style={titleStyle}>
              {title}
            </Text>
          )}
          
          {subtitle && (
            <Text style={subtitleStyle}>
              {subtitle}
            </Text>
          )}
          
          {headerContent && headerContent}
        </View>
      )}
      
      {/* Content */}
      <View style={contentStyle}>
        {children}
      </View>
      
      {/* Footer */}
      {footerContent && (
        <View style={footerStyle}>
          {footerContent}
        </View>
      )}
    </SafeAreaView>
  );
};

// Layout específico para pantallas de perfil
export const WindowsPhoneProfileLayout: React.FC<{
  children: React.ReactNode;
  name: string;
  subtitle?: string;
  avatar?: React.ReactNode;
  backgroundColor?: string;
  isDark?: boolean;
}> = ({
  children,
  name,
  subtitle,
  avatar,
  backgroundColor = WindowsPhoneColors.accent.blue,
  isDark = false,
}) => {
  const theme = getThemeColors(isDark);
  
  const profileHeaderStyle = {
    backgroundColor: backgroundColor,
    paddingHorizontal: SpacingScale.lg,
    paddingVertical: SpacingScale.xl,
    alignItems: 'center' as const,
  };

  const avatarContainerStyle = {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: WindowsPhoneColors.light.background.primary,
    marginBottom: SpacingScale.lg,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    overflow: 'hidden' as const,
  };

  const profileNameStyle = {
    fontSize: 28,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.onAccent,
    textAlign: 'center' as const,
    marginBottom: subtitle ? SpacingScale.xs : 0,
  };

  const profileSubtitleStyle = {
    fontSize: 16,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.onAccent,
    textAlign: 'center' as const,
    opacity: 0.9,
  };

  return (
    <WindowsPhoneLayout
      backgroundColor={theme.background.primary}
      fullScreen={true}
      statusBarStyle="light-content"
    >
      {/* Profile Header */}
      <View style={profileHeaderStyle}>
        <View style={avatarContainerStyle}>
          {avatar}
        </View>
        
        <Text style={profileNameStyle}>
          {name}
        </Text>
        
        {subtitle && (
          <Text style={profileSubtitleStyle}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {/* Profile Content */}
      <View style={{ flex: 1, backgroundColor: theme.background.primary }}>
        {children}
      </View>
    </WindowsPhoneLayout>
  );
};

// Layout para pantallas de comunicación (llamadas, mensajes)
export const WindowsPhoneCommunicationLayout: React.FC<{
  children: React.ReactNode;
  contactName: string;
  contactInfo?: string;
  avatar?: React.ReactNode;
  backgroundColor?: string;
  isDark?: boolean;
}> = ({
  children,
  contactName,
  contactInfo,
  avatar,
  backgroundColor = WindowsPhoneColors.neutral.gray190,
  isDark = true,
}) => {
  const theme = getThemeColors(isDark);
  
  const commHeaderStyle = {
    backgroundColor: backgroundColor,
    paddingHorizontal: SpacingScale.lg,
    paddingVertical: SpacingScale.xl,
    alignItems: 'center' as const,
  };

  const commAvatarStyle = {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: WindowsPhoneColors.light.background.primary,
    marginBottom: SpacingScale.md,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    overflow: 'hidden' as const,
  };

  const commNameStyle = {
    fontSize: 24,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.dark.text.primary,
    textAlign: 'center' as const,
    marginBottom: contactInfo ? SpacingScale.xs : 0,
  };

  const commInfoStyle = {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.dark.text.secondary,
    textAlign: 'center' as const,
  };

  return (
    <WindowsPhoneLayout
      backgroundColor={backgroundColor}
      fullScreen={true}
      statusBarStyle="light-content"
      isDark={isDark}
    >
      {/* Communication Header */}
      <View style={commHeaderStyle}>
        <View style={commAvatarStyle}>
          {avatar}
        </View>
        
        <Text style={commNameStyle}>
          {contactName}
        </Text>
        
        {contactInfo && (
          <Text style={commInfoStyle}>
            {contactInfo}
          </Text>
        )}
      </View>
      
      {/* Communication Content */}
      <View style={{ flex: 1 }}>
        {children}
      </View>
    </WindowsPhoneLayout>
  );
};

// Layouts predefinidos
export const WindowsPhoneDefaultLayout: React.FC<Omit<WindowsPhoneLayoutProps, 'variant'>> = (props) => (
  <WindowsPhoneLayout {...props} variant="default" />
);

export const WindowsPhoneAccentLayout: React.FC<Omit<WindowsPhoneLayoutProps, 'variant'>> = (props) => (
  <WindowsPhoneLayout {...props} variant="accent" backgroundColor={WindowsPhoneColors.accent.blue} isDark={true} statusBarStyle="light-content" />
);

export const WindowsPhoneDarkLayout: React.FC<Omit<WindowsPhoneLayoutProps, 'variant'>> = (props) => (
  <WindowsPhoneLayout {...props} variant="dark" backgroundColor={WindowsPhoneColors.neutral.black} isDark={true} statusBarStyle="light-content" />
);

export const WindowsPhoneFullscreenLayout: React.FC<Omit<WindowsPhoneLayoutProps, 'variant'>> = (props) => (
  <WindowsPhoneLayout {...props} variant="fullscreen" fullScreen={true} />
);

export default WindowsPhoneLayout;
