/**
 * Contenedor principal de autenticación para React Native
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { LoginForm } from './LoginForm';
import { SignupForm } from './SignupForm';
import { UserProfile } from './UserProfile';
import { FluentColors } from '../FluentIconComponent';

type AuthMode = 'login' | 'signup';

export const AuthContainer: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const [authMode, setAuthMode] = useState<AuthMode>('login');

  // Mostrar loading mientras se verifica la sesión
  if (isLoading && !user) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="large" color={FluentColors.primary} />
        <Text className="mt-4 text-gray-600 text-lg">
          Verificando sesión...
        </Text>
      </View>
    );
  }

  // Si está autenticado, mostrar el perfil
  if (isAuthenticated && user) {
    return <UserProfile />;
  }

  // Si no está autenticado, mostrar formularios de login/signup
  return (
    <View className="flex-1 bg-gray-50">
      {authMode === 'login' ? (
        <LoginForm 
          onSwitchToSignup={() => setAuthMode('signup')}
        />
      ) : (
        <SignupForm 
          onSwitchToLogin={() => setAuthMode('login')}
        />
      )}
    </View>
  );
};

export default AuthContainer;
