/**
 * Formulario de Registro para React Native
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';

import { useAuth } from '../../contexts/AuthContext';
import { SignupRequest, FormErrors, UserRole } from '../../types/auth';
import {
  FluentPerson,
  FluentMail,
  FluentLock,
  FluentColors
} from '../FluentIconComponent';
import {
  FluentSuccessButton,
  FluentTransparentButton,
  FluentTextInput,
  FluentEmailInput,
  FluentPasswordInput
} from '../ui';

interface SignupFormProps {
  onSwitchToLogin?: () => void;
}

export const SignupForm: React.FC<SignupFormProps> = ({ onSwitchToLogin }) => {
  const { signup, isLoading, error, clearError } = useAuth();
  
  const [formData, setFormData] = useState<SignupRequest>({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    roles: ['usuario'],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Validación del formulario
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    // Username
    if (!formData.username.trim()) {
      newErrors.username = 'El nombre de usuario es requerido';
    } else if (formData.username.length < 3 || formData.username.length > 20) {
      newErrors.username = 'El nombre de usuario debe tener entre 3 y 20 caracteres';
    }
    
    // Email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Ingresa un email válido';
    } else if (formData.email.length > 50) {
      newErrors.email = 'El email no puede tener más de 50 caracteres';
    }
    
    // Password
    if (!formData.password) {
      newErrors.password = 'La contraseña es requerida';
    } else if (formData.password.length < 6 || formData.password.length > 40) {
      newErrors.password = 'La contraseña debe tener entre 6 y 40 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar envío del formulario
  const handleSubmit = async () => {
    clearError();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      await signup(formData);
    } catch (error) {
      // El error ya se maneja en el contexto
    }
  };

  // Actualizar campo del formulario
  const updateField = (field: keyof SignupRequest, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView 
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <View className="flex-1 justify-center px-6 py-8">
          {/* Header */}
          <View className="items-center mb-8">
            <View className="w-20 h-20 bg-green-100 rounded-full items-center justify-center mb-4">
              <FluentPerson size={40} color={FluentColors.success} />
            </View>
            <Text className="text-3xl font-bold text-gray-900 mb-2">
              Crear Cuenta
            </Text>
            <Text className="text-gray-600 text-center">
              Completa los datos para registrarte
            </Text>
          </View>

          {/* Error general */}
          {error && (
            <View className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <Text className="text-red-800 text-center">{error}</Text>
            </View>
          )}

          {/* Formulario */}
          <View className="space-y-4">
            {/* Campo Username */}
            <FluentTextInput
              label="Nombre de Usuario"
              placeholder="Elige un nombre de usuario"
              value={formData.username}
              onChangeText={(text) => updateField('username', text)}
              leftIcon={<FluentPerson size={20} color={FluentColors.neutral.gray120} />}
              error={errors.username}
              disabled={isLoading}
              autoCapitalize="none"
              autoCorrect={false}
              required
            />

            {/* Campo Email */}
            <FluentEmailInput
              label="Email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChangeText={(text) => updateField('email', text)}
              leftIcon={<FluentMail size={20} color={FluentColors.neutral.gray120} />}
              error={errors.email}
              disabled={isLoading}
              required
            />

            {/* Campo Password */}
            <FluentPasswordInput
              label="Contraseña"
              placeholder="Mínimo 6 caracteres"
              value={formData.password}
              onChangeText={(text) => updateField('password', text)}
              leftIcon={<FluentLock size={20} color={FluentColors.neutral.gray120} />}
              error={errors.password}
              disabled={isLoading}
              required
            />

            {/* Campos opcionales */}
            <View className="flex-row space-x-2">
              <View className="flex-1">
                <FluentTextInput
                  label="Nombre"
                  placeholder="Tu nombre"
                  value={formData.firstName}
                  onChangeText={(text) => updateField('firstName', text)}
                  disabled={isLoading}
                  variant="filled"
                />
              </View>

              <View className="flex-1">
                <FluentTextInput
                  label="Apellido"
                  placeholder="Tu apellido"
                  value={formData.lastName}
                  onChangeText={(text) => updateField('lastName', text)}
                  disabled={isLoading}
                  variant="filled"
                />
              </View>
            </View>
          </View>

          {/* Botón de Registro */}
          <View className="mt-8">
            <FluentSuccessButton
              title={isLoading ? 'Creando cuenta...' : 'Crear Cuenta'}
              onPress={handleSubmit}
              disabled={isLoading}
              loading={isLoading}
              size="large"
              fullWidth
              icon={<FluentPerson size={20} color="white" />}
            />
          </View>

          {/* Link para login */}
          {onSwitchToLogin && (
            <View className="mt-6 items-center">
              <Text className="text-gray-600 mb-3">
                ¿Ya tienes una cuenta?
              </Text>
              <FluentTransparentButton
                title="Inicia sesión aquí"
                onPress={onSwitchToLogin}
                size="medium"
              />
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default SignupForm;
