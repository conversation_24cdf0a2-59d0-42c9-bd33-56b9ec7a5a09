/**
 * Componente para proteger rutas que requieren rol de administrador
 */

import React, { useEffect } from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { router } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types/auth';
import { FluentColors } from '../FluentIconComponent';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ 
  children, 
  fallback 
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();

  const isAdmin = user?.roles?.includes(UserRole.ADMIN);

  useEffect(() => {
    // Si no está cargando y no está autenticado, redirigir a auth
    if (!isLoading && !isAuthenticated) {
      router.replace('/auth');
      return;
    }

    // Si está autenticado pero no es admin, redirigir a home
    if (!isLoading && isAuthenticated && !isAdmin) {
      router.replace('/(tabs)/');
    }
  }, [isAuthenticated, isLoading, isAdmin]);

  // Mostrar loading mientras se verifica la autenticación
  if (isLoading) {
    return fallback || (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="large" color={FluentColors.primary} />
      </View>
    );
  }

  // Si no está autenticado o no es admin, no mostrar nada (se redirigirá)
  if (!isAuthenticated || !isAdmin) {
    return null;
  }

  // Si está autenticado y es admin, mostrar el contenido
  return <>{children}</>;
};

export default AdminProtectedRoute;