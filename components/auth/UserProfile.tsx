/**
 * Componente de Perfil de Usuario para React Native
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { getRoleDisplayName, getRoleColor, UserRole } from '../../types/auth';
import {
  FluentPerson,
  FluentMail,
  FluentSettings,
  FluentColors
} from '../FluentIconComponent';
import {
  FluentPrimaryButton,
  FluentDangerButton
} from '../ui';

export const UserProfile: React.FC = () => {
  const { user, logout, isLoading } = useAuth();

  if (!user) {
    return null;
  }

  // Manejar logout directo
  const handleLogout = () => {
    console.log('🔘 Botón de logout presionado - logout directo');
    logout();
  };

  // Obtener el rol principal (el primero en la lista)
  const primaryRole = user.roles[0] || UserRole.USUARIO;
  const roleColor = getRoleColor(primaryRole);
  const roleDisplayName = getRoleDisplayName(primaryRole);

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="px-6 py-8">
        {/* Header del perfil */}
        <View className="items-center mb-8">
          {/* Avatar */}
          <View 
            className="w-24 h-24 rounded-full items-center justify-center mb-4"
            style={{ backgroundColor: `${roleColor}20` }}
          >
            <FluentPerson size={48} color={roleColor} />
          </View>
          
          {/* Nombre de usuario */}
          <Text className="text-2xl font-ibm-bold text-gray-900 mb-2">
            {user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.username
            }
          </Text>

          {/* Username si hay nombre completo */}
          {user.firstName && user.lastName && (
            <Text className="text-gray-600 mb-2 font-ibm-regular">@{user.username}</Text>
          )}

          {/* Badge de rol */}
          <View
            className="px-3 py-1 rounded-full"
            style={{ backgroundColor: `${roleColor}20` }}
          >
            <Text
              className="text-sm font-ibm-medium"
              style={{ color: roleColor }}
            >
              {roleDisplayName}
            </Text>
          </View>
        </View>

        {/* Información del usuario */}
        <View className="bg-white rounded-lg shadow-sm mb-6">
          <View className="p-4">
            <Text className="text-lg font-ibm-semibold text-gray-900 mb-4">
              Información Personal
            </Text>
            
            {/* Email */}
            <View className="flex-row items-center mb-4">
              <FluentMail size={20} color={FluentColors.neutral.gray120} />
              <View className="ml-3 flex-1">
                <Text className="text-sm text-gray-600 font-ibm-regular">Email</Text>
                <Text className="text-gray-900 font-ibm-medium">{user.email}</Text>
              </View>
            </View>
            
            {/* Username */}
            <View className="flex-row items-center mb-4">
              <FluentPerson size={20} color={FluentColors.neutral.gray120} />
              <View className="ml-3 flex-1">
                <Text className="text-sm text-gray-600">Usuario</Text>
                <Text className="text-gray-900 font-medium">{user.username}</Text>
              </View>
            </View>
            
            {/* Nombre completo si existe */}
            {(user.firstName || user.lastName) && (
              <View className="flex-row items-center">
                <FluentSettings size={20} color={FluentColors.neutral.gray120} />
                <View className="ml-3 flex-1">
                  <Text className="text-sm text-gray-600">Nombre Completo</Text>
                  <Text className="text-gray-900 font-medium">
                    {[user.firstName, user.lastName].filter(Boolean).join(' ') || 'No especificado'}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Roles del usuario */}
        <View className="bg-white rounded-lg shadow-sm mb-6">
          <View className="p-4">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
              Roles y Permisos
            </Text>
            
            <View className="space-y-2">
              {user.roles.map((role, index) => {
                const color = getRoleColor(role);
                const displayName = getRoleDisplayName(role);
                
                return (
                  <View 
                    key={index}
                    className="flex-row items-center justify-between p-3 rounded-lg"
                    style={{ backgroundColor: `${color}10` }}
                  >
                    <Text 
                      className="font-medium"
                      style={{ color }}
                    >
                      {displayName}
                    </Text>
                    
                    {index === 0 && (
                      <View 
                        className="px-2 py-1 rounded"
                        style={{ backgroundColor: `${color}20` }}
                      >
                        <Text 
                          className="text-xs font-medium"
                          style={{ color }}
                        >
                          Principal
                        </Text>
                      </View>
                    )}
                  </View>
                );
              })}
            </View>
          </View>
        </View>

        {/* Estadísticas básicas */}
        <View className="bg-white rounded-lg shadow-sm mb-6">
          <View className="p-4">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
              Información de Cuenta
            </Text>
            
            <View className="flex-row justify-between items-center">
              <View className="items-center flex-1">
                <Text className="text-2xl font-bold text-blue-600">
                  {user.id}
                </Text>
                <Text className="text-sm text-gray-600">ID de Usuario</Text>
              </View>
              
              <View className="items-center flex-1">
                <Text className="text-2xl font-bold text-green-600">
                  {user.roles.length}
                </Text>
                <Text className="text-sm text-gray-600">
                  {user.roles.length === 1 ? 'Rol' : 'Roles'}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Acciones */}
        <View className="space-y-3">
          {/* Botón de editar perfil (placeholder) */}
          <FluentPrimaryButton
            title="Editar Perfil"
            size="large"
            fullWidth
            onPress={() => Alert.alert('Próximamente', 'La edición de perfil estará disponible pronto')}
            icon={<FluentSettings size={20} color="white" />}
          />

          {/* Botón de logout */}
          <FluentDangerButton
            title={isLoading ? 'Cerrando sesión...' : 'Cerrar Sesión'}
            size="large"
            fullWidth
            onPress={handleLogout}
            disabled={isLoading}
            loading={isLoading}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default UserProfile;
