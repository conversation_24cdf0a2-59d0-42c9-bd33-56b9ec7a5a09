/**
 * Formulario de Login para React Native
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { LoginRequest, FormErrors } from '../../types/auth';
import { FluentPerson, FluentLock, FluentColors } from '../FluentIconComponent';
import {
  FluentPrimaryButton,
  FluentTransparentButton,
  FluentTextInput,
  FluentPasswordInput
} from '../ui';

interface LoginFormProps {
  onSwitchToSignup?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSwitchToSignup }) => {
  const { login, isLoading, error, clearError } = useAuth();
  
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Validación del formulario
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formData.username.trim()) {
      newErrors.username = 'El nombre de usuario es requerido';
    } else if (formData.username.length < 3) {
      newErrors.username = 'El nombre de usuario debe tener al menos 3 caracteres';
    }
    
    if (!formData.password) {
      newErrors.password = 'La contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar envío del formulario
  const handleSubmit = async () => {
    clearError();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      await login(formData);
    } catch (error) {
      // El error ya se maneja en el contexto
    }
  };

  // Actualizar campo del formulario
  const updateField = (field: keyof LoginRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView 
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <View className="flex-1 justify-center px-6 py-8">
          {/* Header */}
          <View className="items-center mb-8">
            <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mb-4">
              <svg width="50" height="50" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M17.754 14a2.249 2.249 0 0 1 2.25 2.249v.918a2.75 2.75 0 0 1-.513 1.599C17.945 20.929 15.42 22 12 22c-3.422 0-5.945-1.072-7.487-3.237a2.75 2.75 0 0 1-.51-1.595v-.92a2.249 2.249 0 0 1 2.249-2.25h11.501ZM12 2.004a5 5 0 1 1 0 10 5 5 0 0 1 0-10Z" fill="#3398DB"/></svg>
            </View>
            <Text className="text-3xl font-bold text-gray-900 mb-2 font-ibm-semibold">
              Iniciar Sesión
            </Text>
            <Text className="text-gray-600 text-center">
              Ingresa tus credenciales para acceder
            </Text>
          </View>

          {/* Error general */}
          {error && (
            <View className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <Text className="text-red-800 text-center">{error}</Text>
            </View>
          )}

          {/* Formulario */}
          <View className="space-y-4">
            {/* Campo Username */}
            <FluentTextInput
              label="Nombre de Usuario"
              placeholder="Ingresa tu usuario"
              value={formData.username}
              onChangeText={(text) => updateField('username', text)}
              leftIcon={<FluentPerson size={20} color={FluentColors.neutral.gray120} />}
              error={errors.username}
              disabled={isLoading}
              autoCapitalize="none"
              autoCorrect={false}
              required
            />

            {/* Campo Password */}
            <FluentPasswordInput
              label="Contraseña"
              placeholder="Ingresa tu contraseña"
              value={formData.password}
              onChangeText={(text) => updateField('password', text)}
              leftIcon={<FluentLock size={20} color={FluentColors.neutral.gray120} />}
              error={errors.password}
              disabled={isLoading}
              required
            />
          </View>

          {/* Botón de Login */}
          <View className="mt-8">
            <FluentPrimaryButton
              title={isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
              onPress={handleSubmit}
              disabled={isLoading}
              loading={isLoading}
              size="large"
              fullWidth
              icon={<FluentPerson size={20} color="white" />}
            />
          </View>

          {/* Link para registro */}
          {onSwitchToSignup && (
            <View className="mt-6 items-center">
              <Text className="text-gray-600 mb-3">
                ¿No tienes una cuenta?
              </Text>
              <FluentTransparentButton
                title="Regístrate aquí"
                onPress={onSwitchToSignup}
                size="medium"
              />
            </View>
          )}

        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default LoginForm;
