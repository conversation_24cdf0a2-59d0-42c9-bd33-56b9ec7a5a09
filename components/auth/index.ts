/**
 * Exportaciones de componentes de autenticación
 */

export { default as AuthContainer } from './AuthContainer';
export { LoginForm } from './LoginForm';
export { SignupForm } from './SignupForm';
export { UserProfile } from './UserProfile';
export { default as ProtectedRoute } from './ProtectedRoute';

// Re-exportar el contexto y hook
export { AuthProvider, useAuth } from '../../contexts/AuthContext';

// Re-exportar tipos
export * from '../../types/auth';

// Re-exportar servicio
export { default as authService } from '../../services/authService';
