import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { WindowsPhoneColors, getAccentColor, getNeutralColor } from '../constants/WindowsPhoneColors';
import { SpacingScale, ComponentSpacing } from '../constants/Spacing';

const WindowsPhoneColorDemo = () => {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>
        Sistema de Colores Windows Phone UI
      </Text>

      {/* Estado de implementación */}
      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>
          ✅ Sistema de Colores Expandido
        </Text>
        <Text style={styles.infoText}>
          Nuevo sistema de colores Windows Phone UI implementado:
        </Text>
        <Text style={styles.infoText}>
          • 12 colores de acento vibrantes
        </Text>
        <Text style={styles.infoText}>
          • Soporte para modo claro y oscuro
        </Text>
        <Text style={styles.infoText}>
          • Tokens de diseño en Tailwind CSS
        </Text>
        <Text style={styles.infoText}>
          • Sistema de espaciado consistente
        </Text>
        <Text style={styles.infoText}>
          • Compatibilidad con componentes existentes
        </Text>
      </View>

      {/* Colores de acento */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Colores de Acento Windows Phone
        </Text>
        
        <View style={styles.colorGrid}>
          {Object.entries(WindowsPhoneColors.accent).map(([name, color]) => (
            <View key={name} style={styles.colorItem}>
              <View style={[styles.colorSwatch, { backgroundColor: color }]} />
              <Text style={styles.colorName}>{name}</Text>
              <Text style={styles.colorValue}>{color}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Colores semánticos */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Colores Semánticos
        </Text>
        
        <View style={styles.colorRow}>
          {Object.entries(WindowsPhoneColors.status).map(([name, color]) => (
            <View key={name} style={styles.statusItem}>
              <View style={[styles.statusSwatch, { backgroundColor: color }]} />
              <Text style={styles.statusName}>{name}</Text>
              <Text style={styles.statusValue}>{color}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Tema claro */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Tema Claro (Light Theme)
        </Text>
        
        <View style={styles.themeDemo}>
          <View style={[styles.themeCard, { backgroundColor: WindowsPhoneColors.light.background.primary }]}>
            <Text style={[styles.themeTitle, { color: WindowsPhoneColors.light.text.primary }]}>
              Tarjeta de Ejemplo
            </Text>
            <Text style={[styles.themeText, { color: WindowsPhoneColors.light.text.secondary }]}>
              Este es un ejemplo de cómo se ve el texto en el tema claro.
            </Text>
            <View style={[styles.themeButton, { backgroundColor: WindowsPhoneColors.light.background.accent }]}>
              <Text style={[styles.themeButtonText, { color: WindowsPhoneColors.light.text.onAccent }]}>
                Botón de Acción
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Tema oscuro */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Tema Oscuro (Dark Theme)
        </Text>
        
        <View style={styles.themeDemo}>
          <View style={[styles.themeCard, { backgroundColor: WindowsPhoneColors.dark.background.primary }]}>
            <Text style={[styles.themeTitle, { color: WindowsPhoneColors.dark.text.primary }]}>
              Tarjeta de Ejemplo
            </Text>
            <Text style={[styles.themeText, { color: WindowsPhoneColors.dark.text.secondary }]}>
              Este es un ejemplo de cómo se ve el texto en el tema oscuro.
            </Text>
            <View style={[styles.themeButton, { backgroundColor: WindowsPhoneColors.dark.background.accent }]}>
              <Text style={[styles.themeButtonText, { color: WindowsPhoneColors.dark.text.onAccent }]}>
                Botón de Acción
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Escala de grises */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Escala de Grises (Neutral)
        </Text>
        
        <View style={styles.grayScale}>
          {Object.entries(WindowsPhoneColors.neutral).slice(0, 12).map(([name, color]) => (
            <View key={name} style={styles.grayItem}>
              <View style={[styles.graySwatch, { backgroundColor: color }]} />
              <Text style={styles.grayName}>{name}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Clases de Tailwind */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Clases de Tailwind CSS Disponibles
        </Text>
        
        <View style={styles.tailwindDemo}>
          <Text style={styles.codeText}>
            {`// Colores de acento
text-wp-blue, bg-wp-blue
text-wp-cyan, bg-wp-cyan
text-wp-green, bg-wp-green

// Colores semánticos
text-wp-success, bg-wp-success
text-wp-warning, bg-wp-warning
text-wp-error, bg-wp-error

// Tema claro
text-wp-text-primary
bg-wp-bg-primary
border-wp-border-primary

// Tema oscuro
text-wp-dark-text-primary
bg-wp-dark-bg-primary
border-wp-dark-border-primary`}
          </Text>
        </View>
      </View>

      {/* Utilidades */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Funciones Utilitarias
        </Text>
        
        <View style={styles.utilityDemo}>
          <Text style={styles.utilityText}>
            <Text style={styles.utilityLabel}>getAccentColor(0.5):</Text> {getAccentColor(0.5)}
          </Text>
          <Text style={styles.utilityText}>
            <Text style={styles.utilityLabel}>getNeutralColor('gray120'):</Text> {getNeutralColor('gray120')}
          </Text>
          
          <View style={styles.utilityExample}>
            <View style={[styles.utilityBox, { backgroundColor: getAccentColor(0.1) }]}>
              <Text style={styles.utilityBoxText}>Acento 10% opacidad</Text>
            </View>
            <View style={[styles.utilityBox, { backgroundColor: getAccentColor(0.3) }]}>
              <Text style={styles.utilityBoxText}>Acento 30% opacidad</Text>
            </View>
            <View style={[styles.utilityBox, { backgroundColor: getAccentColor(0.5) }]}>
              <Text style={[styles.utilityBoxText, { color: 'white' }]}>Acento 50% opacidad</Text>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: WindowsPhoneColors.light.background.primary,
    padding: SpacingScale.lg,
  },
  title: {
    fontSize: 24,
    fontFamily: 'IBMPlexSans-Bold',
    textAlign: 'center',
    marginBottom: SpacingScale.xl,
    color: WindowsPhoneColors.light.text.primary,
  },
  section: {
    marginBottom: SpacingScale.xl,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'IBMPlexSans-SemiBold',
    marginBottom: SpacingScale.lg,
    color: WindowsPhoneColors.light.text.primary,
  },
  infoSection: {
    backgroundColor: WindowsPhoneColors.light.background.tertiary,
    padding: SpacingScale.lg,
    borderRadius: 8,
    marginBottom: SpacingScale.xl,
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.primary,
  },
  infoTitle: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: WindowsPhoneColors.accent.blue,
    marginBottom: SpacingScale.sm,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.secondary,
    marginBottom: 4,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SpacingScale.md,
  },
  colorItem: {
    alignItems: 'center',
    width: '22%',
  },
  colorSwatch: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: SpacingScale.xs,
  },
  colorName: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Medium',
    color: WindowsPhoneColors.light.text.primary,
    textAlign: 'center',
  },
  colorValue: {
    fontSize: 10,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.tertiary,
    textAlign: 'center',
  },
  colorRow: {
    flexDirection: 'row',
    gap: SpacingScale.md,
  },
  statusItem: {
    flex: 1,
    alignItems: 'center',
  },
  statusSwatch: {
    width: 50,
    height: 50,
    borderRadius: 6,
    marginBottom: SpacingScale.xs,
  },
  statusName: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Medium',
    color: WindowsPhoneColors.light.text.primary,
    textAlign: 'center',
  },
  statusValue: {
    fontSize: 10,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.tertiary,
    textAlign: 'center',
  },
  themeDemo: {
    padding: SpacingScale.md,
  },
  themeCard: {
    padding: SpacingScale.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.primary,
  },
  themeTitle: {
    fontSize: 16,
    fontFamily: 'IBMPlexSans-SemiBold',
    marginBottom: SpacingScale.sm,
  },
  themeText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    marginBottom: SpacingScale.lg,
    lineHeight: 20,
  },
  themeButton: {
    paddingVertical: SpacingScale.sm,
    paddingHorizontal: SpacingScale.lg,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  themeButtonText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Medium',
  },
  grayScale: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SpacingScale.sm,
  },
  grayItem: {
    alignItems: 'center',
    width: '15%',
  },
  graySwatch: {
    width: 40,
    height: 40,
    borderRadius: 4,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.secondary,
  },
  grayName: {
    fontSize: 9,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.tertiary,
    textAlign: 'center',
  },
  tailwindDemo: {
    backgroundColor: WindowsPhoneColors.neutral.gray190,
    padding: SpacingScale.lg,
    borderRadius: 6,
  },
  codeText: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Regular',
    color: WindowsPhoneColors.neutral.gray20,
    lineHeight: 18,
  },
  utilityDemo: {
    backgroundColor: WindowsPhoneColors.light.background.secondary,
    padding: SpacingScale.lg,
    borderRadius: 6,
  },
  utilityText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.primary,
    marginBottom: SpacingScale.sm,
  },
  utilityLabel: {
    fontFamily: 'IBMPlexSans-Medium',
  },
  utilityExample: {
    flexDirection: 'row',
    gap: SpacingScale.sm,
    marginTop: SpacingScale.md,
  },
  utilityBox: {
    flex: 1,
    padding: SpacingScale.md,
    borderRadius: 4,
    alignItems: 'center',
  },
  utilityBoxText: {
    fontSize: 12,
    fontFamily: 'IBMPlexSans-Medium',
    textAlign: 'center',
  },
});

export default WindowsPhoneColorDemo;
