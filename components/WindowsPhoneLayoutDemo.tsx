import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { 
  WindowsPhoneLayout,
  WindowsPhoneAccentLayout,
  WindowsPhoneDarkLayout,
  WindowsPhoneBottomNav,
  WindowsPhoneTopNav,
  WindowsPhoneTabNav,
  WindowsPhoneProfile,
  WindowsPhoneProfileHero,
  WindowsPhoneCheckbox,
  WindowsPhoneRadio,
  WindowsPhoneToggle,
  WindowsPhoneDropdown,
  FluentPrimaryButton,
} from './ui';
import { FluentHome, FluentPerson, FluentSettings, FluentMail } from './FluentIconComponent';
import { WindowsPhoneColors } from '../constants/WindowsPhoneColors';
import { SpacingScale } from '../constants/Spacing';
import WindowsPhoneScreenExample from './examples/WindowsPhoneScreenExample';

const WindowsPhoneLayoutDemo = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [radioValue, setRadioValue] = useState('option1');
  const [toggleValue, setToggleValue] = useState(true);
  const [dropdownValue, setDropdownValue] = useState('');

  const dropdownOptions = [
    { label: 'Opción 1', value: 'option1' },
    { label: 'Opción 2', value: 'option2' },
    { label: 'Opción 3', value: 'option3' },
  ];

  const tabs = [
    { key: 'profile', title: 'Profile', icon: <FluentPerson /> },
    { key: 'settings', title: 'Settings', icon: <FluentSettings /> },
    { key: 'mail', title: 'Mail', icon: <FluentMail /> },
  ];

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>
        Layouts y Estructura Windows Phone
      </Text>

      {/* Estado de implementación */}
      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>
          ✅ Layouts y Estructura Completos
        </Text>
        <Text style={styles.infoText}>
          Sistema completo de layouts y componentes estructurales:
        </Text>
        <Text style={styles.infoText}>
          • Layouts principales (Default, Accent, Dark, Fullscreen)
        </Text>
        <Text style={styles.infoText}>
          • Navegación inferior estilo Windows Phone
        </Text>
        <Text style={styles.infoText}>
          • Navegación superior con títulos
        </Text>
        <Text style={styles.infoText}>
          • Navegación por pestañas
        </Text>
        <Text style={styles.infoText}>
          • Perfiles con avatares circulares
        </Text>
        <Text style={styles.infoText}>
          • Controles nativos (checkbox, radio, toggle, dropdown)
        </Text>
      </View>

      {/* Layout Default */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Layout Default
        </Text>
        
        <View style={styles.layoutDemo}>
          <WindowsPhoneLayout
            title="Torrance Shum"
            subtitle="Mobile: (*************, Seattle, WA"
            style={styles.layoutContainer}
          >
            <WindowsPhoneProfile
              name="Torrance Shum"
              subtitle="Mobile: (*************"
              description="Seattle, WA"
              size="large"
              centerContent={true}
            />
            
            <View style={styles.buttonContainer}>
              <FluentPrimaryButton
                title="Answer"
                onPress={() => console.log('Answer')}
                size="medium"
                style={styles.actionButton}
              />
              <FluentPrimaryButton
                title="Message"
                onPress={() => console.log('Message')}
                variant="secondary"
                size="medium"
                style={styles.actionButton}
              />
              <FluentPrimaryButton
                title="Ignore"
                onPress={() => console.log('Ignore')}
                variant="outline"
                size="medium"
                style={styles.actionButton}
              />
            </View>
          </WindowsPhoneLayout>
        </View>
      </View>

      {/* Layout Accent */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Layout Accent (Azul)
        </Text>
        
        <View style={styles.layoutDemo}>
          <WindowsPhoneAccentLayout
            title="AMY COOK"
            style={styles.layoutContainer}
            headerContent={
              <WindowsPhoneTabNav
                tabs={[
                  { key: 'profile', title: 'Profile' },
                  { key: 'whats-new', title: "What's new" },
                ]}
                activeTab="profile"
                onTabPress={() => {}}
                isDark={true}
              />
            }
          >
            <WindowsPhoneProfileHero
              name="Amy Cook"
              subtitle="Unlocked an achievement in Minecraft for 40"
              description="Adventuring Time: Discover 17 of the 23 different biomes"
              variant="accent"
              showStatus={true}
              statusText="Xbox about an hour ago"
            />
            
            <View style={styles.accentActions}>
              <Text style={styles.accentActionText}>Message</Text>
              <Text style={styles.accentActionText}>Call mobile</Text>
              <Text style={styles.accentActionText}>Call on an app</Text>
              <Text style={styles.accentActionText}>Video call</Text>
              <Text style={styles.accentActionText}>Email work</Text>
            </View>
          </WindowsPhoneAccentLayout>
        </View>
      </View>

      {/* Controles Nativos */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Controles Nativos Windows Phone
        </Text>
        
        <View style={styles.controlsContainer}>
          <WindowsPhoneCheckbox
            label="Action Text"
            checked={checkboxValue}
            onToggle={setCheckboxValue}
          />

          <WindowsPhoneRadio
            label="Action Text"
            selected={radioValue === 'option1'}
            onSelect={() => setRadioValue('option1')}
          />

          <WindowsPhoneRadio
            label="Action Text"
            selected={radioValue === 'option2'}
            onSelect={() => setRadioValue('option2')}
          />

          <WindowsPhoneToggle
            label="Control label"
            value={toggleValue}
            onToggle={setToggleValue}
          />

          <WindowsPhoneDropdown
            label="Selected list item"
            value={dropdownValue}
            options={dropdownOptions}
            onSelect={setDropdownValue}
            placeholder="Seleccionar opción..."
          />
        </View>
      </View>

      {/* Navegación */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Navegación Windows Phone
        </Text>
        
        {/* Navegación Superior */}
        <View style={styles.navDemo}>
          <Text style={styles.navLabel}>Navegación Superior:</Text>
          <WindowsPhoneTopNav
            title="Laura Stevens"
            subtitle="Fake GSM Network"
            leftAction={<FluentHome size={20} />}
            rightAction={<FluentSettings size={20} />}
          />
        </View>
        
        {/* Navegación por Pestañas */}
        <View style={styles.navDemo}>
          <Text style={styles.navLabel}>Navegación por Pestañas:</Text>
          <WindowsPhoneTabNav
            tabs={tabs}
            activeTab={activeTab}
            onTabPress={setActiveTab}
          />
        </View>
        
        {/* Navegación Inferior */}
        <View style={styles.navDemo}>
          <Text style={styles.navLabel}>Navegación Inferior:</Text>
          <WindowsPhoneBottomNav
            onBackPress={() => console.log('Back')}
            onHomePress={() => console.log('Home')}
            onSearchPress={() => console.log('Search')}
          />
        </View>
      </View>

      {/* Ejemplo de Pantalla Completa */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Ejemplo de Pantalla Completa
        </Text>

        <View style={styles.fullScreenDemo}>
          <WindowsPhoneScreenExample />
        </View>
      </View>

      {/* Información técnica */}
      <View style={styles.techSection}>
        <Text style={styles.techTitle}>
          🏗️ Arquitectura Completa
        </Text>
        <Text style={styles.techText}>
          • WindowsPhoneLayout - Layout principal con variantes
        </Text>
        <Text style={styles.techText}>
          • WindowsPhoneNavigation - Navegación inferior/superior/tabs
        </Text>
        <Text style={styles.techText}>
          • WindowsPhoneProfile - Perfiles con avatares
        </Text>
        <Text style={styles.techText}>
          • WindowsPhoneControls - Checkbox, Radio, Toggle, Dropdown
        </Text>
        <Text style={styles.techText}>
          • Sistema de colores consistente en todos los componentes
        </Text>
        <Text style={styles.techText}>
          • Espaciado y tipografía unificados
        </Text>
        <Text style={styles.techText}>
          • Soporte para modo claro/oscuro
        </Text>
        <Text style={styles.techText}>
          • Ejemplo de pantalla completa funcional
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: WindowsPhoneColors.light.background.primary,
    padding: SpacingScale.lg,
  },
  title: {
    fontSize: 24,
    fontFamily: 'IBMPlexSans-Bold',
    textAlign: 'center',
    marginBottom: SpacingScale.xl,
    color: WindowsPhoneColors.light.text.primary,
  },
  section: {
    marginBottom: SpacingScale.xl,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'IBMPlexSans-SemiBold',
    marginBottom: SpacingScale.lg,
    color: WindowsPhoneColors.light.text.primary,
  },
  infoSection: {
    backgroundColor: WindowsPhoneColors.light.background.tertiary,
    padding: SpacingScale.lg,
    borderRadius: 8,
    marginBottom: SpacingScale.xl,
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.primary,
  },
  infoTitle: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: WindowsPhoneColors.accent.blue,
    marginBottom: SpacingScale.sm,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.secondary,
    marginBottom: 4,
  },
  layoutDemo: {
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.primary,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: SpacingScale.lg,
  },
  layoutContainer: {
    minHeight: 200,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SpacingScale.lg,
    gap: SpacingScale.sm,
  },
  actionButton: {
    flex: 1,
  },
  accentActions: {
    marginTop: SpacingScale.lg,
  },
  accentActionText: {
    fontSize: 16,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.onAccent,
    paddingVertical: SpacingScale.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.2)',
  },
  controlsContainer: {
    gap: SpacingScale.md,
  },
  navDemo: {
    marginBottom: SpacingScale.lg,
  },
  navLabel: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Medium',
    color: WindowsPhoneColors.light.text.secondary,
    marginBottom: SpacingScale.sm,
  },
  techSection: {
    backgroundColor: WindowsPhoneColors.light.background.secondary,
    padding: SpacingScale.lg,
    borderRadius: 8,
    marginBottom: SpacingScale.xl,
  },
  techTitle: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: WindowsPhoneColors.light.text.primary,
    marginBottom: SpacingScale.sm,
  },
  techText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.secondary,
    marginBottom: 4,
  },
  fullScreenDemo: {
    height: 600,
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.primary,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: WindowsPhoneColors.accent.blue,
  },
});

export default WindowsPhoneLayoutDemo;
