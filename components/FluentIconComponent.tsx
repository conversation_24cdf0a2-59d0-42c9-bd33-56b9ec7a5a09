import React from 'react';
import { View, ViewStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { WindowsPhoneColors, FluentColors as NewFluentColors } from '../constants/WindowsPhoneColors';

// Tipos de iconos disponibles
type IconLibrary = 'MaterialIcons' | 'MaterialCommunityIcons' | 'Ionicons';

// Props del componente FluentIcon
interface FluentIconProps {
  name: string;
  size?: number;
  color?: string;
  library?: IconLibrary;
  style?: ViewStyle;
  filled?: boolean; // Para simular filled/regular de Fluent UI
}

// Componente FluentIcon que simula el comportamiento de Fluent UI
const FluentIcon: React.FC<FluentIconProps> = ({
  name,
  size = 24,
  color = '#000000',
  library = 'MaterialIcons',
  style,
  filled = false,
}) => {
  // Función para obtener el nombre del icono basado en si es filled o regular
  const getIconName = (baseName: string, isFilled: boolean) => {
    if (library === 'MaterialIcons') {
      // Material Icons ya maneja filled/outlined automáticamente
      return baseName;
    } else if (library === 'MaterialCommunityIcons') {
      // Para Material Community Icons, algunos tienen variantes -outline
      return isFilled ? baseName : `${baseName}-outline`;
    } else if (library === 'Ionicons') {
      // Ionicons usa sufijos diferentes
      return isFilled ? baseName : `${baseName}-outline`;
    }
    return baseName;
  };

  const iconName = getIconName(name, filled);

  // Renderizar el icono según la biblioteca
  const renderIcon = () => {
    const commonProps = {
      name: iconName,
      size,
      color,
    };

    switch (library) {
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons {...commonProps} />;
      case 'Ionicons':
        return <Ionicons {...commonProps} />;
      case 'MaterialIcons':
      default:
        return <Icon {...commonProps} />;
    }
  };

  return (
    <View style={[{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }, style]}>
      {renderIcon()}
    </View>
  );
};

// Componentes predefinidos con estilo Fluent UI
export const FluentHome = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="home" library="MaterialIcons" {...props} />
);

export const FluentHeart = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="favorite" library="MaterialIcons" {...props} />
);

export const FluentPerson = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="person" library="MaterialIcons" {...props} />
);

export const FluentSettings = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="settings" library="MaterialIcons" {...props} />
);

export const FluentSearch = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="search" library="MaterialIcons" {...props} />
);

export const FluentCart = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="shopping-cart" library="MaterialIcons" {...props} />
);

export const FluentStar = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="star" library="MaterialIcons" {...props} />
);

export const FluentMail = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="mail" library="MaterialIcons" {...props} />
);

export const FluentCalendar = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="event" library="MaterialIcons" {...props} />
);

export const FluentDocument = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="description" library="MaterialIcons" {...props} />
);

export const FluentLock = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="lock" library="MaterialIcons" {...props} />
);

// Iconos adicionales usando Material Community Icons
export const FluentAccount = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="account-circle" library="MaterialCommunityIcons" {...props} />
);

export const FluentBell = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="bell" library="MaterialCommunityIcons" {...props} />
);

export const FluentCamera = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="camera" library="MaterialCommunityIcons" {...props} />
);

export const FluentShare = (props: Omit<FluentIconProps, 'name' | 'library'>) => (
  <FluentIcon name="share" library="MaterialCommunityIcons" {...props} />
);

// Función helper para crear iconos personalizados
export const createFluentIcon = (
  name: string, 
  library: IconLibrary = 'MaterialIcons'
) => {
  return (props: Omit<FluentIconProps, 'name' | 'library'>) => (
    <FluentIcon name={name} library={library} {...props} />
  );
};

// Colores predefinidos estilo Windows Phone UI (mantener compatibilidad)
export const FluentColors = {
  primary: WindowsPhoneColors.accent.blue,
  secondary: WindowsPhoneColors.accent.indigo,
  success: WindowsPhoneColors.status.success,
  warning: WindowsPhoneColors.status.warning,
  error: WindowsPhoneColors.status.error,
  neutral: WindowsPhoneColors.neutral,

  // Nuevos colores de acento Windows Phone
  accent: WindowsPhoneColors.accent,

  // Colores de tema
  light: WindowsPhoneColors.light,
  dark: WindowsPhoneColors.dark,
};

// Exportar también el sistema completo
export { WindowsPhoneColors };

export default FluentIcon;
