import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, TextInputProps, StyleSheet } from 'react-native';
import { WindowsPhoneColors, getThemeColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale, ComponentSpacing } from '../../constants/Spacing';

// Tipos de input Fluent UI
export type FluentInputVariant = 'default' | 'filled' | 'underlined';
export type FluentInputSize = 'small' | 'medium' | 'large';

// Props del input
export interface FluentTextInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  variant?: FluentInputVariant;
  size?: FluentInputSize;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  helperText?: string;
  style?: any;
  inputStyle?: any;
}

// Función para obtener estilos Windows Phone según la variante
const getInputStyles = (variant: FluentInputVariant, size: FluentInputSize, hasError: boolean, isFocused: boolean, disabled: boolean, isDark: boolean = false) => {
  const theme = getThemeColors(isDark);

  // Dimensiones según el tamaño
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: ComponentSpacing.input.small.horizontal,
          paddingVertical: ComponentSpacing.input.small.vertical,
          fontSize: 14,
          minHeight: 32,
        };
      case 'large':
        return {
          paddingHorizontal: ComponentSpacing.input.large.horizontal,
          paddingVertical: ComponentSpacing.input.large.vertical,
          fontSize: 18,
          minHeight: 48,
        };
      default: // medium
        return {
          paddingHorizontal: ComponentSpacing.input.medium.horizontal,
          paddingVertical: ComponentSpacing.input.medium.vertical,
          fontSize: 16,
          minHeight: 40,
        };
    }
  };

  // Estados de color Windows Phone
  const getColors = () => {
    if (disabled) {
      return {
        border: theme.border.disabled,
        background: theme.surface.disabled,
        text: theme.text.disabled,
        placeholder: theme.text.disabled,
      };
    }
    if (hasError) {
      return {
        border: WindowsPhoneColors.status.error,
        background: theme.background.primary,
        text: theme.text.primary,
        placeholder: theme.text.tertiary,
      };
    }
    if (isFocused) {
      return {
        border: WindowsPhoneColors.accent.blue,
        background: theme.background.primary,
        text: theme.text.primary,
        placeholder: theme.text.tertiary,
      };
    }
    return {
      border: theme.border.primary,
      background: theme.background.primary,
      text: theme.text.primary,
      placeholder: theme.text.tertiary,
    };
  };

  const colors = getColors();
  const dimensions = getDimensions();

  switch (variant) {
    case 'filled':
      return {
        ...dimensions,
        backgroundColor: disabled ? theme.surface.disabled : theme.background.secondary,
        borderColor: colors.border,
        borderWidth: 1,
        borderRadius: 4,
        textColor: colors.text,
        placeholderColor: colors.placeholder,
      };

    case 'underlined':
      return {
        ...dimensions,
        backgroundColor: 'transparent',
        borderColor: colors.border,
        borderWidth: 0,
        borderBottomWidth: 2,
        borderRadius: 0,
        textColor: colors.text,
        placeholderColor: colors.placeholder,
      };

    default: // 'default'
      return {
        ...dimensions,
        backgroundColor: colors.background,
        borderColor: colors.border,
        borderWidth: 1,
        borderRadius: 4,
        textColor: colors.text,
        placeholderColor: colors.placeholder,
      };
  }
};

// Función para obtener tamaño de texto
const getTextSize = (size: FluentInputSize) => {
  switch (size) {
    case 'small':
      return 'text-sm';
    case 'large':
      return 'text-lg';
    default:
      return 'text-base';
  }
};

export const FluentTextInput: React.FC<FluentTextInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  variant = 'default',
  size = 'medium',
  error,
  disabled = false,
  required = false,
  leftIcon,
  rightIcon,
  onRightIconPress,
  helperText,
  style,
  inputStyle,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const inputStyles = getInputStyles(variant, size, !!error, isFocused, disabled);
  const hasError = !!error;
  const theme = getThemeColors(false);

  const containerStyle = {
    backgroundColor: inputStyles.backgroundColor,
    borderColor: inputStyles.borderColor,
    borderWidth: inputStyles.borderWidth,
    borderBottomWidth: inputStyles.borderBottomWidth || inputStyles.borderWidth,
    borderRadius: inputStyles.borderRadius,
    paddingHorizontal: inputStyles.paddingHorizontal,
    paddingVertical: inputStyles.paddingVertical,
    minHeight: inputStyles.minHeight,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  };

  const textInputStyle = {
    flex: 1,
    color: inputStyles.textColor,
    fontSize: inputStyles.fontSize,
    fontFamily: 'IBMPlexSans-Light',
    ...inputStyle,
  };

  const labelStyle = {
    color: theme.text.primary,
    fontSize: inputStyles.fontSize - 2,
    fontFamily: 'IBMPlexSans-Medium',
    marginBottom: SpacingScale.xs,
  };

  const helperTextStyle = {
    color: hasError ? WindowsPhoneColors.status.error : theme.text.secondary,
    fontSize: inputStyles.fontSize - 2,
    fontFamily: 'IBMPlexSans-Light',
    marginTop: SpacingScale.xs,
  };

  return (
    <View style={style}>
      {/* Label */}
      {label && (
        <View>
          <Text style={labelStyle}>
            {label}
            {required && (
              <Text style={{ color: WindowsPhoneColors.status.error }}> *</Text>
            )}
          </Text>
        </View>
      )}

      {/* Input Container */}
      <View style={containerStyle}>
        {/* Left Icon */}
        {leftIcon && (
          <View style={{ marginRight: SpacingScale.sm }}>
            {leftIcon}
          </View>
        )}

        {/* Text Input */}
        <TextInput
          style={textInputStyle}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={inputStyles.placeholderColor}
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...textInputProps}
        />

        {/* Right Icon */}
        {rightIcon && (
          <TouchableOpacity
            style={{ marginLeft: SpacingScale.sm }}
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
            activeOpacity={0.7}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>

      {/* Helper Text or Error */}
      {(helperText || error) && (
        <View>
          <Text style={helperTextStyle}>
            {error || helperText}
          </Text>
        </View>
      )}
    </View>
  );
};

// Componentes predefinidos para casos específicos
export const FluentPasswordInput: React.FC<Omit<FluentTextInputProps, 'secureTextEntry' | 'rightIcon' | 'onRightIconPress'> & {
  showPasswordIcon?: React.ReactNode;
  hidePasswordIcon?: React.ReactNode;
}> = ({ 
  showPasswordIcon, 
  hidePasswordIcon, 
  ...props 
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const defaultShowIcon = <Text className="text-blue-600 font-ibm-medium">👁️</Text>;
  const defaultHideIcon = <Text className="text-blue-600 font-ibm-medium">🙈</Text>;

  return (
    <FluentTextInput
      {...props}
      secureTextEntry={!showPassword}
      rightIcon={showPassword ? (hidePasswordIcon || defaultHideIcon) : (showPasswordIcon || defaultShowIcon)}
      onRightIconPress={() => setShowPassword(!showPassword)}
    />
  );
};

export const FluentEmailInput: React.FC<Omit<FluentTextInputProps, 'keyboardType' | 'autoCapitalize' | 'autoCorrect'>> = (props) => (
  <FluentTextInput
    {...props}
    keyboardType="email-address"
    autoCapitalize="none"
    autoCorrect={false}
  />
);

export const FluentSearchInput: React.FC<Omit<FluentTextInputProps, 'leftIcon'> & {
  searchIcon?: React.ReactNode;
}> = ({ searchIcon, ...props }) => {
  const defaultSearchIcon = <Text className="text-gray-400">🔍</Text>;
  
  return (
    <FluentTextInput
      {...props}
      leftIcon={searchIcon || defaultSearchIcon}
      variant="filled"
    />
  );
};

export default FluentTextInput;
