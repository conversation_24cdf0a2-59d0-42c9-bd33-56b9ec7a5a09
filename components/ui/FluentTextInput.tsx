import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, TextInputProps } from 'react-native';
import { FluentColors } from '../FluentIconComponent';

// Tipos de input Fluent UI
export type FluentInputVariant = 'default' | 'filled' | 'underlined';
export type FluentInputSize = 'small' | 'medium' | 'large';

// Props del input
export interface FluentTextInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  variant?: FluentInputVariant;
  size?: FluentInputSize;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  helperText?: string;
  style?: any;
  inputStyle?: any;
}

// Función para obtener estilos según la variante
const getInputStyles = (variant: FluentInputVariant, size: FluentInputSize, hasError: boolean, isFocused: boolean, disabled: boolean) => {
  const baseInputStyle = 'flex-row items-center';
  
  // Tamaños
  const sizeStyles = {
    small: 'px-3 py-2',
    medium: 'px-3 py-3',
    large: 'px-4 py-4',
  };

  // Estados de color
  const getColors = () => {
    if (disabled) {
      return {
        border: '#e5e7eb',
        background: '#f9fafb',
        text: '#9ca3af',
      };
    }
    if (hasError) {
      return {
        border: FluentColors.error,
        background: '#ffffff',
        text: '#1f2937',
      };
    }
    if (isFocused) {
      return {
        border: FluentColors.primary,
        background: '#ffffff',
        text: '#1f2937',
      };
    }
    return {
      border: '#d1d5db',
      background: '#ffffff',
      text: '#1f2937',
    };
  };

  const colors = getColors();

  switch (variant) {
    case 'filled':
      return {
        container: `${baseInputStyle} ${sizeStyles[size]} bg-gray-50 border border-gray-300 rounded`,
        input: 'flex-1 font-ibm-light',
        colors: {
          ...colors,
          background: disabled ? '#f3f4f6' : '#f9fafb',
        },
      };
    
    case 'underlined':
      return {
        container: `${baseInputStyle} ${sizeStyles[size]} border-b-2`,
        input: 'flex-1 font-ibm-light',
        colors,
      };
    
    default: // 'default'
      return {
        container: `${baseInputStyle} ${sizeStyles[size]} bg-white border border-gray-300 rounded`,
        input: 'flex-1 font-ibm-light',
        colors,
      };
  }
};

// Función para obtener tamaño de texto
const getTextSize = (size: FluentInputSize) => {
  switch (size) {
    case 'small':
      return 'text-sm';
    case 'large':
      return 'text-lg';
    default:
      return 'text-base';
  }
};

export const FluentTextInput: React.FC<FluentTextInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  variant = 'default',
  size = 'medium',
  error,
  disabled = false,
  required = false,
  leftIcon,
  rightIcon,
  onRightIconPress,
  helperText,
  style,
  inputStyle,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  
  const styles = getInputStyles(variant, size, !!error, isFocused, disabled);
  const textSize = getTextSize(size);
  const hasError = !!error;

  return (
    <View style={style}>
      {/* Label */}
      {label && (
        <View className="mb-2">
          <Text className={`${textSize} font-ibm-medium text-gray-700`}>
            {label}
            {required && <Text className="text-red-500 ml-1">*</Text>}
          </Text>
        </View>
      )}

      {/* Input Container */}
      <View 
        className={styles.container}
        style={{
          borderColor: styles.colors.border,
          backgroundColor: styles.colors.background,
        }}
      >
        {/* Left Icon */}
        {leftIcon && (
          <View className="mr-3">
            {leftIcon}
          </View>
        )}

        {/* Text Input */}
        <TextInput
          className={`${styles.input} ${textSize}`}
          style={[
            {
              color: styles.colors.text,
              fontFamily: 'IBMPlexSans-Light',
            },
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="#9ca3af"
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...textInputProps}
        />

        {/* Right Icon */}
        {rightIcon && (
          <TouchableOpacity 
            className="ml-3"
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>

      {/* Helper Text or Error */}
      {(helperText || error) && (
        <View className="mt-1">
          <Text 
            className={`text-sm font-ibm-light ${
              hasError ? 'text-red-600' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </Text>
        </View>
      )}
    </View>
  );
};

// Componentes predefinidos para casos específicos
export const FluentPasswordInput: React.FC<Omit<FluentTextInputProps, 'secureTextEntry' | 'rightIcon' | 'onRightIconPress'> & {
  showPasswordIcon?: React.ReactNode;
  hidePasswordIcon?: React.ReactNode;
}> = ({ 
  showPasswordIcon, 
  hidePasswordIcon, 
  ...props 
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const defaultShowIcon = <Text className="text-blue-600 font-ibm-medium">👁️</Text>;
  const defaultHideIcon = <Text className="text-blue-600 font-ibm-medium">🙈</Text>;

  return (
    <FluentTextInput
      {...props}
      secureTextEntry={!showPassword}
      rightIcon={showPassword ? (hidePasswordIcon || defaultHideIcon) : (showPasswordIcon || defaultShowIcon)}
      onRightIconPress={() => setShowPassword(!showPassword)}
    />
  );
};

export const FluentEmailInput: React.FC<Omit<FluentTextInputProps, 'keyboardType' | 'autoCapitalize' | 'autoCorrect'>> = (props) => (
  <FluentTextInput
    {...props}
    keyboardType="email-address"
    autoCapitalize="none"
    autoCorrect={false}
  />
);

export const FluentSearchInput: React.FC<Omit<FluentTextInputProps, 'leftIcon'> & {
  searchIcon?: React.ReactNode;
}> = ({ searchIcon, ...props }) => {
  const defaultSearchIcon = <Text className="text-gray-400">🔍</Text>;
  
  return (
    <FluentTextInput
      {...props}
      leftIcon={searchIcon || defaultSearchIcon}
      variant="filled"
    />
  );
};

export default FluentTextInput;
