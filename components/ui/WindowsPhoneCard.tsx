import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { WindowsPhoneColors, getThemeColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale, ComponentSpacing } from '../../constants/Spacing';

// Tipos de tarjetas Windows Phone
export type WindowsPhoneCardVariant = 
  | 'primary' 
  | 'secondary' 
  | 'accent'
  | 'neutral'
  | 'success'
  | 'warning'
  | 'error';

// Tamaños de tarjetas
export type WindowsPhoneCardSize = 'small' | 'medium' | 'large' | 'wide';

// Props de la tarjeta
export interface WindowsPhoneCardProps {
  title?: string;
  subtitle?: string;
  content?: React.ReactNode;
  icon?: React.ReactNode;
  variant?: WindowsPhoneCardVariant;
  size?: WindowsPhoneCardSize;
  onPress?: () => void;
  disabled?: boolean;
  style?: any;
  isDark?: boolean;
}

// Función para obtener colores según la variante
const getCardColors = (variant: WindowsPhoneCardVariant, isDark: boolean = false) => {
  const theme = getThemeColors(isDark);
  
  switch (variant) {
    case 'primary':
      return {
        backgroundColor: WindowsPhoneColors.accent.blue,
        textColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
      };
    
    case 'secondary':
      return {
        backgroundColor: theme.background.secondary,
        textColor: theme.text.primary,
        subtitleColor: theme.text.secondary,
      };
    
    case 'accent':
      return {
        backgroundColor: WindowsPhoneColors.accent.cyan,
        textColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
      };
    
    case 'neutral':
      return {
        backgroundColor: theme.background.card,
        textColor: theme.text.primary,
        subtitleColor: theme.text.secondary,
      };
    
    case 'success':
      return {
        backgroundColor: WindowsPhoneColors.status.success,
        textColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
      };
    
    case 'warning':
      return {
        backgroundColor: WindowsPhoneColors.status.warning,
        textColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
      };
    
    case 'error':
      return {
        backgroundColor: WindowsPhoneColors.status.error,
        textColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
      };
    
    default:
      return {
        backgroundColor: WindowsPhoneColors.accent.blue,
        textColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
      };
  }
};

// Función para obtener dimensiones según el tamaño
const getCardDimensions = (size: WindowsPhoneCardSize) => {
  switch (size) {
    case 'small':
      return {
        width: 120,
        height: 120,
        padding: SpacingScale.md,
        titleSize: 14,
        subtitleSize: 12,
        iconSize: 32,
      };
    
    case 'medium':
      return {
        width: 160,
        height: 160,
        padding: SpacingScale.lg,
        titleSize: 16,
        subtitleSize: 14,
        iconSize: 40,
      };
    
    case 'large':
      return {
        width: 200,
        height: 200,
        padding: SpacingScale.xl,
        titleSize: 18,
        subtitleSize: 16,
        iconSize: 48,
      };
    
    case 'wide':
      return {
        width: '100%',
        height: 120,
        padding: SpacingScale.lg,
        titleSize: 16,
        subtitleSize: 14,
        iconSize: 40,
      };
    
    default:
      return {
        width: 160,
        height: 160,
        padding: SpacingScale.lg,
        titleSize: 16,
        subtitleSize: 14,
        iconSize: 40,
      };
  }
};

export const WindowsPhoneCard: React.FC<WindowsPhoneCardProps> = ({
  title,
  subtitle,
  content,
  icon,
  variant = 'primary',
  size = 'medium',
  onPress,
  disabled = false,
  style,
  isDark = false,
}) => {
  const colors = getCardColors(variant, isDark);
  const dimensions = getCardDimensions(size);
  
  const cardStyle = {
    backgroundColor: colors.backgroundColor,
    width: dimensions.width,
    height: dimensions.height,
    padding: dimensions.padding,
    borderRadius: 4, // Windows Phone usa esquinas muy sutiles
    opacity: disabled ? 0.6 : 1,
  };

  const titleStyle = {
    color: colors.textColor,
    fontSize: dimensions.titleSize,
    fontFamily: 'IBMPlexSans-SemiBold',
    marginBottom: subtitle ? SpacingScale.xs : 0,
  };

  const subtitleStyle = {
    color: colors.subtitleColor,
    fontSize: dimensions.subtitleSize,
    fontFamily: 'IBMPlexSans-Light',
    opacity: 0.9,
  };

  const CardContent = () => (
    <View style={styles.cardContainer}>
      {/* Icono */}
      {icon && (
        <View style={[styles.iconContainer, { marginBottom: title || subtitle ? SpacingScale.md : 0 }]}>
          {React.cloneElement(icon as React.ReactElement, {
            size: dimensions.iconSize,
            color: colors.textColor,
          })}
        </View>
      )}
      
      {/* Contenido de texto */}
      <View style={styles.textContainer}>
        {title && (
          <Text style={titleStyle} numberOfLines={2}>
            {title}
          </Text>
        )}
        
        {subtitle && (
          <Text style={subtitleStyle} numberOfLines={1}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {/* Contenido personalizado */}
      {content && (
        <View style={styles.customContent}>
          {content}
        </View>
      )}
    </View>
  );

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        style={[cardStyle, style]}
        onPress={onPress}
        activeOpacity={0.8}
        disabled={disabled}
      >
        <CardContent />
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      <CardContent />
    </View>
  );
};

// Componentes predefinidos para casos comunes
export const WindowsPhonePrimaryCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="primary" />
);

export const WindowsPhoneSecondaryCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="secondary" />
);

export const WindowsPhoneAccentCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="accent" />
);

export const WindowsPhoneNeutralCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="neutral" />
);

export const WindowsPhoneSuccessCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="success" />
);

export const WindowsPhoneWarningCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="warning" />
);

export const WindowsPhoneErrorCard: React.FC<Omit<WindowsPhoneCardProps, 'variant'>> = (props) => (
  <WindowsPhoneCard {...props} variant="error" />
);

const styles = StyleSheet.create({
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  customContent: {
    flex: 1,
    width: '100%',
  },
});

export default WindowsPhoneCard;
