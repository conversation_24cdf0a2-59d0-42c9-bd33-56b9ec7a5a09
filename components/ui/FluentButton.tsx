import React from 'react';
import { TouchableOpacity, Text, View, ActivityIndicator } from 'react-native';
import { WindowsPhoneColors, getThemeColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale, ComponentSpacing } from '../../constants/Spacing';

// Tipos de botones Fluent UI
export type FluentButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'subtle' 
  | 'outline' 
  | 'transparent'
  | 'danger'
  | 'success';

// Tamaños de botones
export type FluentButtonSize = 'small' | 'medium' | 'large';

// Props del botón
export interface FluentButtonProps {
  title: string;
  onPress: () => void;
  variant?: FluentButtonVariant;
  size?: FluentButtonSize;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: any;
  textStyle?: any;
}

// Función para obtener estilos Windows Phone según la variante
const getButtonStyles = (variant: FluentButtonVariant, disabled: boolean, loading: boolean, isDark: boolean = false) => {
  const theme = getThemeColors(isDark);

  if (disabled || loading) {
    return {
      backgroundColor: theme.surface.disabled,
      textColor: theme.text.disabled,
      borderColor: 'transparent',
      borderWidth: 0,
    };
  }

  switch (variant) {
    case 'primary':
      return {
        backgroundColor: WindowsPhoneColors.accent.blue,
        textColor: WindowsPhoneColors.light.text.onAccent,
        borderColor: 'transparent',
        borderWidth: 0,
      };

    case 'secondary':
      return {
        backgroundColor: theme.surface.default,
        textColor: theme.text.primary,
        borderColor: theme.border.primary,
        borderWidth: 1,
      };

    case 'subtle':
      return {
        backgroundColor: theme.surface.hover,
        textColor: theme.text.primary,
        borderColor: 'transparent',
        borderWidth: 0,
      };

    case 'outline':
      return {
        backgroundColor: 'transparent',
        textColor: WindowsPhoneColors.accent.blue,
        borderColor: WindowsPhoneColors.accent.blue,
        borderWidth: 1,
      };

    case 'transparent':
      return {
        backgroundColor: 'transparent',
        textColor: WindowsPhoneColors.accent.blue,
        borderColor: 'transparent',
        borderWidth: 0,
      };

    case 'danger':
      return {
        backgroundColor: WindowsPhoneColors.status.error,
        textColor: WindowsPhoneColors.light.text.onAccent,
        borderColor: 'transparent',
        borderWidth: 0,
      };

    case 'success':
      return {
        backgroundColor: WindowsPhoneColors.status.success,
        textColor: WindowsPhoneColors.light.text.onAccent,
        borderColor: 'transparent',
        borderWidth: 0,
      };

    default:
      return {
        backgroundColor: WindowsPhoneColors.accent.blue,
        textColor: WindowsPhoneColors.light.text.onAccent,
        borderColor: 'transparent',
        borderWidth: 0,
      };
  }
};

// Función para obtener padding según el tamaño
const getSizePadding = (size: FluentButtonSize) => {
  switch (size) {
    case 'small':
      return 'px-3 py-2';
    case 'medium':
      return 'px-4 py-3';
    case 'large':
      return 'px-6 py-4';
    default:
      return 'px-4 py-3';
  }
};

// Función para obtener tamaño de texto
const getTextSize = (size: FluentButtonSize) => {
  switch (size) {
    case 'small':
      return 'text-sm';
    case 'medium':
      return 'text-base';
    case 'large':
      return 'text-lg';
    default:
      return 'text-base';
  }
};

export const FluentButton: React.FC<FluentButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
}) => {
  const styles = getButtonStyles(variant, disabled, loading);
  const padding = getSizePadding(size);
  const textSize = getTextSize(size);
  
  const isDisabled = disabled || loading;

  return (
    <TouchableOpacity
      className={`${styles.container} ${padding} ${fullWidth ? 'w-full' : ''}`}
      onPress={onPress}
      disabled={isDisabled}
      style={style}
      activeOpacity={0.8}
    >
      {/* Icono izquierdo */}
      {icon && iconPosition === 'left' && (
        <View className="mr-2">
          {icon}
        </View>
      )}
      
      {/* Loading indicator */}
      {loading && (
        <View className="mr-2">
          <ActivityIndicator 
            size="small" 
            color={variant === 'primary' || variant === 'danger' || variant === 'success' ? 'white' : FluentColors.primary} 
          />
        </View>
      )}
      
      {/* Texto del botón */}
      <Text 
        className={`${styles.text} ${textSize}`}
        style={textStyle}
      >
        {title}
      </Text>
      
      {/* Icono derecho */}
      {icon && iconPosition === 'right' && (
        <View className="ml-2">
          {icon}
        </View>
      )}
    </TouchableOpacity>
  );
};

// Componentes predefinidos para casos comunes
export const FluentPrimaryButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="primary" />
);

export const FluentSecondaryButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="secondary" />
);

export const FluentOutlineButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="outline" />
);

export const FluentDangerButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="danger" />
);

export const FluentSuccessButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="success" />
);

export const FluentSubtleButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="subtle" />
);

export const FluentTransparentButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="transparent" />
);

export default FluentButton;
