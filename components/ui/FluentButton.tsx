import React from 'react';
import { TouchableOpacity, Text, View, ActivityIndicator } from 'react-native';
import { FluentColors } from '../FluentIconComponent';

// Tipos de botones Fluent UI
export type FluentButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'subtle' 
  | 'outline' 
  | 'transparent'
  | 'danger'
  | 'success';

// Tamaños de botones
export type FluentButtonSize = 'small' | 'medium' | 'large';

// Props del botón
export interface FluentButtonProps {
  title: string;
  onPress: () => void;
  variant?: FluentButtonVariant;
  size?: FluentButtonSize;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: any;
  textStyle?: any;
}

// Función para obtener estilos según la variante
const getButtonStyles = (variant: FluentButtonVariant, disabled: boolean, loading: boolean) => {
  const baseStyle = 'rounded flex-row items-center justify-center';
  
  if (disabled || loading) {
    return {
      container: `${baseStyle} opacity-50`,
      text: 'text-gray-400',
      background: 'bg-gray-100',
    };
  }

  switch (variant) {
    case 'primary':
      return {
        container: `${baseStyle} bg-blue-600 active:bg-blue-700`,
        text: 'text-white font-ibm-semibold',
        background: 'bg-blue-600',
      };
    
    case 'secondary':
      return {
        container: `${baseStyle} bg-gray-100 active:bg-gray-200 border border-gray-300`,
        text: 'text-gray-900 font-ibm-medium',
        background: 'bg-gray-100',
      };

    case 'subtle':
      return {
        container: `${baseStyle} bg-gray-50 active:bg-gray-100`,
        text: 'text-gray-700 font-ibm-medium',
        background: 'bg-gray-50',
      };

    case 'outline':
      return {
        container: `${baseStyle} bg-transparent border-2 border-blue-600 active:bg-blue-50`,
        text: 'text-blue-600 font-ibm-medium',
        background: 'bg-transparent',
      };

    case 'transparent':
      return {
        container: `${baseStyle} bg-transparent active:bg-gray-100`,
        text: 'text-blue-600 font-ibm-medium',
        background: 'bg-transparent',
      };

    case 'danger':
      return {
        container: `${baseStyle} bg-red-600 active:bg-red-700`,
        text: 'text-white font-ibm-semibold',
        background: 'bg-red-600',
      };

    case 'success':
      return {
        container: `${baseStyle} bg-green-600 active:bg-green-700`,
        text: 'text-white font-ibm-semibold',
        background: 'bg-green-600',
      };
    
    default:
      return {
        container: `${baseStyle} bg-blue-600 active:bg-blue-700`,
        text: 'text-white font-ibm-semibold',
        background: 'bg-blue-600',
      };
  }
};

// Función para obtener padding según el tamaño
const getSizePadding = (size: FluentButtonSize) => {
  switch (size) {
    case 'small':
      return 'px-3 py-2';
    case 'medium':
      return 'px-4 py-3';
    case 'large':
      return 'px-6 py-4';
    default:
      return 'px-4 py-3';
  }
};

// Función para obtener tamaño de texto
const getTextSize = (size: FluentButtonSize) => {
  switch (size) {
    case 'small':
      return 'text-sm';
    case 'medium':
      return 'text-base';
    case 'large':
      return 'text-lg';
    default:
      return 'text-base';
  }
};

export const FluentButton: React.FC<FluentButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
}) => {
  const styles = getButtonStyles(variant, disabled, loading);
  const padding = getSizePadding(size);
  const textSize = getTextSize(size);
  
  const isDisabled = disabled || loading;

  return (
    <TouchableOpacity
      className={`${styles.container} ${padding} ${fullWidth ? 'w-full' : ''}`}
      onPress={onPress}
      disabled={isDisabled}
      style={style}
      activeOpacity={0.8}
    >
      {/* Icono izquierdo */}
      {icon && iconPosition === 'left' && (
        <View className="mr-2">
          {icon}
        </View>
      )}
      
      {/* Loading indicator */}
      {loading && (
        <View className="mr-2">
          <ActivityIndicator 
            size="small" 
            color={variant === 'primary' || variant === 'danger' || variant === 'success' ? 'white' : FluentColors.primary} 
          />
        </View>
      )}
      
      {/* Texto del botón */}
      <Text 
        className={`${styles.text} ${textSize}`}
        style={textStyle}
      >
        {title}
      </Text>
      
      {/* Icono derecho */}
      {icon && iconPosition === 'right' && (
        <View className="ml-2">
          {icon}
        </View>
      )}
    </TouchableOpacity>
  );
};

// Componentes predefinidos para casos comunes
export const FluentPrimaryButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="primary" />
);

export const FluentSecondaryButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="secondary" />
);

export const FluentOutlineButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="outline" />
);

export const FluentDangerButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="danger" />
);

export const FluentSuccessButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="success" />
);

export const FluentSubtleButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="subtle" />
);

export const FluentTransparentButton: React.FC<Omit<FluentButtonProps, 'variant'>> = (props) => (
  <FluentButton {...props} variant="transparent" />
);

export default FluentButton;
