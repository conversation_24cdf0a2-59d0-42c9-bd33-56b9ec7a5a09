import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { WindowsPhoneColors, getThemeColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale } from '../../constants/Spacing';

// Tipos de perfil
export type WindowsPhoneProfileSize = 'small' | 'medium' | 'large' | 'hero';
export type WindowsPhoneProfileVariant = 'default' | 'accent' | 'dark';

// Props del perfil
export interface WindowsPhoneProfileProps {
  name: string;
  subtitle?: string;
  description?: string;
  avatarUri?: string;
  size?: WindowsPhoneProfileSize;
  variant?: WindowsPhoneProfileVariant;
  showStatus?: boolean;
  statusText?: string;
  centerContent?: boolean;
  isDark?: boolean;
  style?: any;
}

// Función para obtener dimensiones del avatar
const getAvatarDimensions = (size: WindowsPhoneProfileSize) => {
  switch (size) {
    case 'small':
      return {
        avatarSize: 40,
        nameSize: 16,
        subtitleSize: 14,
        descriptionSize: 12,
        spacing: SpacingScale.sm,
      };
    case 'medium':
      return {
        avatarSize: 60,
        nameSize: 18,
        subtitleSize: 16,
        descriptionSize: 14,
        spacing: SpacingScale.md,
      };
    case 'large':
      return {
        avatarSize: 80,
        nameSize: 20,
        subtitleSize: 18,
        descriptionSize: 16,
        spacing: SpacingScale.lg,
      };
    case 'hero':
      return {
        avatarSize: 120,
        nameSize: 28,
        subtitleSize: 20,
        descriptionSize: 16,
        spacing: SpacingScale.xl,
      };
    default:
      return {
        avatarSize: 60,
        nameSize: 18,
        subtitleSize: 16,
        descriptionSize: 14,
        spacing: SpacingScale.md,
      };
  }
};

// Función para obtener colores del perfil
const getProfileColors = (variant: WindowsPhoneProfileVariant, isDark: boolean = false) => {
  const theme = getThemeColors(isDark);
  
  switch (variant) {
    case 'accent':
      return {
        backgroundColor: WindowsPhoneColors.accent.blue,
        nameColor: WindowsPhoneColors.light.text.onAccent,
        subtitleColor: WindowsPhoneColors.light.text.onAccent,
        descriptionColor: WindowsPhoneColors.light.text.onAccent,
        statusColor: WindowsPhoneColors.light.text.onAccent,
      };
    
    case 'dark':
      return {
        backgroundColor: WindowsPhoneColors.neutral.black,
        nameColor: WindowsPhoneColors.neutral.white,
        subtitleColor: WindowsPhoneColors.neutral.gray50,
        descriptionColor: WindowsPhoneColors.neutral.gray100,
        statusColor: WindowsPhoneColors.neutral.gray50,
      };
    
    default: // 'default'
      return {
        backgroundColor: 'transparent',
        nameColor: theme.text.primary,
        subtitleColor: theme.text.secondary,
        descriptionColor: theme.text.tertiary,
        statusColor: theme.text.secondary,
      };
  }
};

export const WindowsPhoneProfile: React.FC<WindowsPhoneProfileProps> = ({
  name,
  subtitle,
  description,
  avatarUri,
  size = 'medium',
  variant = 'default',
  showStatus = false,
  statusText,
  centerContent = false,
  isDark = false,
  style,
}) => {
  const dimensions = getAvatarDimensions(size);
  const colors = getProfileColors(variant, isDark);

  const containerStyle = {
    backgroundColor: colors.backgroundColor,
    padding: variant !== 'default' ? dimensions.spacing : 0,
    flexDirection: centerContent ? 'column' : 'row',
    alignItems: centerContent ? 'center' : 'flex-start',
    ...style,
  };

  const avatarContainerStyle = {
    width: dimensions.avatarSize,
    height: dimensions.avatarSize,
    borderRadius: dimensions.avatarSize / 2,
    backgroundColor: WindowsPhoneColors.neutral.gray200,
    marginRight: centerContent ? 0 : dimensions.spacing,
    marginBottom: centerContent ? dimensions.spacing : 0,
    overflow: 'hidden' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  const avatarStyle = {
    width: dimensions.avatarSize,
    height: dimensions.avatarSize,
    borderRadius: dimensions.avatarSize / 2,
  };

  const avatarPlaceholderStyle = {
    fontSize: dimensions.avatarSize * 0.4,
    color: WindowsPhoneColors.neutral.gray500,
    fontFamily: 'IBMPlexSans-Light',
  };

  const contentContainerStyle = {
    flex: centerContent ? 0 : 1,
    alignItems: centerContent ? 'center' : 'flex-start',
  };

  const nameStyle = {
    fontSize: dimensions.nameSize,
    fontFamily: size === 'hero' ? 'IBMPlexSans-Light' : 'IBMPlexSans-Medium',
    color: colors.nameColor,
    textAlign: centerContent ? 'center' : 'left',
    marginBottom: subtitle ? SpacingScale.xs : 0,
  };

  const subtitleStyle = {
    fontSize: dimensions.subtitleSize,
    fontFamily: 'IBMPlexSans-Light',
    color: colors.subtitleColor,
    textAlign: centerContent ? 'center' : 'left',
    marginBottom: description ? SpacingScale.xs : 0,
  };

  const descriptionStyle = {
    fontSize: dimensions.descriptionSize,
    fontFamily: 'IBMPlexSans-Light',
    color: colors.descriptionColor,
    textAlign: centerContent ? 'center' : 'left',
    lineHeight: dimensions.descriptionSize * 1.4,
    marginBottom: showStatus && statusText ? SpacingScale.xs : 0,
  };

  const statusStyle = {
    fontSize: dimensions.descriptionSize,
    fontFamily: 'IBMPlexSans-Light',
    color: colors.statusColor,
    textAlign: centerContent ? 'center' : 'left',
    opacity: 0.8,
  };

  return (
    <View style={containerStyle}>
      {/* Avatar */}
      <View style={avatarContainerStyle}>
        {avatarUri ? (
          <Image source={{ uri: avatarUri }} style={avatarStyle} />
        ) : (
          <Text style={avatarPlaceholderStyle}>
            {name.charAt(0).toUpperCase()}
          </Text>
        )}
      </View>

      {/* Content */}
      <View style={contentContainerStyle}>
        {/* Name */}
        <Text style={nameStyle} numberOfLines={centerContent ? 2 : 1}>
          {name}
        </Text>

        {/* Subtitle */}
        {subtitle && (
          <Text style={subtitleStyle} numberOfLines={1}>
            {subtitle}
          </Text>
        )}

        {/* Description */}
        {description && (
          <Text style={descriptionStyle} numberOfLines={centerContent ? 3 : 2}>
            {description}
          </Text>
        )}

        {/* Status */}
        {showStatus && statusText && (
          <Text style={statusStyle} numberOfLines={1}>
            {statusText}
          </Text>
        )}
      </View>
    </View>
  );
};

// Componentes predefinidos
export const WindowsPhoneProfileSmall: React.FC<Omit<WindowsPhoneProfileProps, 'size'>> = (props) => (
  <WindowsPhoneProfile {...props} size="small" />
);

export const WindowsPhoneProfileMedium: React.FC<Omit<WindowsPhoneProfileProps, 'size'>> = (props) => (
  <WindowsPhoneProfile {...props} size="medium" />
);

export const WindowsPhoneProfileLarge: React.FC<Omit<WindowsPhoneProfileProps, 'size'>> = (props) => (
  <WindowsPhoneProfile {...props} size="large" />
);

export const WindowsPhoneProfileHero: React.FC<Omit<WindowsPhoneProfileProps, 'size'>> = (props) => (
  <WindowsPhoneProfile {...props} size="hero" centerContent={true} />
);

export const WindowsPhoneProfileAccent: React.FC<Omit<WindowsPhoneProfileProps, 'variant'>> = (props) => (
  <WindowsPhoneProfile {...props} variant="accent" />
);

export const WindowsPhoneProfileDark: React.FC<Omit<WindowsPhoneProfileProps, 'variant'>> = (props) => (
  <WindowsPhoneProfile {...props} variant="dark" />
);

export default WindowsPhoneProfile;
