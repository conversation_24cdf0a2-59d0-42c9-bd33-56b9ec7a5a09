import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { FluentColors } from '../FluentIconComponent';

// Tipos de botones de icono
export type FluentIconButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'subtle' 
  | 'transparent'
  | 'danger';

// Tamaños de botones de icono
export type FluentIconButtonSize = 'small' | 'medium' | 'large';

// Props del botón de icono
export interface FluentIconButtonProps {
  icon: React.ReactNode;
  onPress: () => void;
  variant?: FluentIconButtonVariant;
  size?: FluentIconButtonSize;
  disabled?: boolean;
  style?: any;
  accessibilityLabel?: string;
}

// Función para obtener estilos según la variante
const getIconButtonStyles = (variant: FluentIconButtonVariant, disabled: boolean) => {
  const baseStyle = 'rounded items-center justify-center';
  
  if (disabled) {
    return {
      container: `${baseStyle} opacity-50 bg-gray-100`,
      background: '#f3f4f6',
    };
  }

  switch (variant) {
    case 'primary':
      return {
        container: `${baseStyle} bg-blue-600 active:bg-blue-700`,
        background: FluentColors.primary,
      };
    
    case 'secondary':
      return {
        container: `${baseStyle} bg-gray-100 active:bg-gray-200 border border-gray-300`,
        background: '#f3f4f6',
      };
    
    case 'subtle':
      return {
        container: `${baseStyle} bg-gray-50 active:bg-gray-100`,
        background: '#f9fafb',
      };
    
    case 'transparent':
      return {
        container: `${baseStyle} bg-transparent active:bg-gray-100`,
        background: 'transparent',
      };
    
    case 'danger':
      return {
        container: `${baseStyle} bg-red-600 active:bg-red-700`,
        background: FluentColors.error,
      };
    
    default:
      return {
        container: `${baseStyle} bg-transparent active:bg-gray-100`,
        background: 'transparent',
      };
  }
};

// Función para obtener dimensiones según el tamaño
const getSizeDimensions = (size: FluentIconButtonSize) => {
  switch (size) {
    case 'small':
      return { width: 32, height: 32, iconSize: 16 };
    case 'medium':
      return { width: 40, height: 40, iconSize: 20 };
    case 'large':
      return { width: 48, height: 48, iconSize: 24 };
    default:
      return { width: 40, height: 40, iconSize: 20 };
  }
};

export const FluentIconButton: React.FC<FluentIconButtonProps> = ({
  icon,
  onPress,
  variant = 'transparent',
  size = 'medium',
  disabled = false,
  style,
  accessibilityLabel,
}) => {
  const styles = getIconButtonStyles(variant, disabled);
  const dimensions = getSizeDimensions(size);

  return (
    <TouchableOpacity
      className={styles.container}
      onPress={onPress}
      disabled={disabled}
      style={[
        {
          width: dimensions.width,
          height: dimensions.height,
        },
        style,
      ]}
      activeOpacity={0.8}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="button"
    >
      <View>
        {React.cloneElement(icon as React.ReactElement, {
          size: dimensions.iconSize,
        })}
      </View>
    </TouchableOpacity>
  );
};

// Componentes predefinidos para casos comunes
export const FluentPrimaryIconButton: React.FC<Omit<FluentIconButtonProps, 'variant'>> = (props) => (
  <FluentIconButton {...props} variant="primary" />
);

export const FluentSecondaryIconButton: React.FC<Omit<FluentIconButtonProps, 'variant'>> = (props) => (
  <FluentIconButton {...props} variant="secondary" />
);

export const FluentSubtleIconButton: React.FC<Omit<FluentIconButtonProps, 'variant'>> = (props) => (
  <FluentIconButton {...props} variant="subtle" />
);

export const FluentTransparentIconButton: React.FC<Omit<FluentIconButtonProps, 'variant'>> = (props) => (
  <FluentIconButton {...props} variant="transparent" />
);

export const FluentDangerIconButton: React.FC<Omit<FluentIconButtonProps, 'variant'>> = (props) => (
  <FluentIconButton {...props} variant="danger" />
);

export default FluentIconButton;
