/**
 * Exportaciones de componentes UI con estilo Fluent UI
 */

// Botones
export {
  FluentButton,
  FluentPrimaryButton,
  FluentSecondaryButton,
  FluentOutlineButton,
  FluentDangerButton,
  FluentSuccessButton,
  FluentSubtleButton,
  FluentTransparentButton,
  type FluentButtonProps,
  type FluentButtonVariant,
  type FluentButtonSize,
} from './FluentButton';

// Botones de icono
export {
  FluentIconButton,
  FluentPrimaryIconButton,
  FluentSecondaryIconButton,
  FluentSubtleIconButton,
  FluentTransparentIconButton,
  FluentDangerIconButton,
  type FluentIconButtonProps,
  type FluentIconButtonVariant,
  type FluentIconButtonSize,
} from './FluentIconButton';

// Inputs
export {
  FluentTextInput,
  FluentPasswordInput,
  FluentEmailInput,
  FluentSearchInput,
  type FluentTextInputProps,
  type FluentInputVariant,
  type FluentInputSize,
} from './FluentTextInput';

// Re-exportar componente existente
export { IconSymbol } from './IconSymbol';
