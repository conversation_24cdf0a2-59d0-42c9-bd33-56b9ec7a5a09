/**
 * Exportaciones de componentes UI con estilo Fluent UI
 */

// Botones
export {
  FluentButton,
  FluentPrimaryButton,
  FluentSecondaryButton,
  FluentOutlineButton,
  FluentDangerButton,
  FluentSuccessButton,
  FluentSubtleButton,
  FluentTransparentButton,
  type FluentButtonProps,
  type FluentButtonVariant,
  type FluentButtonSize,
} from './FluentButton';

// Botones de icono
export {
  FluentIconButton,
  FluentPrimaryIconButton,
  FluentSecondaryIconButton,
  FluentSubtleIconButton,
  FluentTransparentIconButton,
  FluentDangerIconButton,
  type FluentIconButtonProps,
  type FluentIconButtonVariant,
  type FluentIconButtonSize,
} from './FluentIconButton';

// Inputs
export {
  FluentTextInput,
  FluentPasswordInput,
  FluentEmailInput,
  FluentSearchInput,
  type FluentTextInputProps,
  type FluentInputVariant,
  type FluentInputSize,
} from './FluentTextInput';

// Windows Phone Cards
export {
  WindowsPhoneCard,
  WindowsPhonePrimaryCard,
  WindowsPhoneSecondaryCard,
  WindowsPhoneAccentCard,
  WindowsPhoneNeutralCard,
  WindowsPhoneSuccessCard,
  WindowsPhoneWarningCard,
  WindowsPhoneErrorCard,
  type WindowsPhoneCardProps,
  type WindowsPhoneCardVariant,
  type WindowsPhoneCardSize,
} from './WindowsPhoneCard';

// Windows Phone Controls
export {
  WindowsPhoneCheckbox,
  WindowsPhoneRadio,
  WindowsPhoneToggle,
  WindowsPhoneDropdown,
  type WindowsPhoneCheckboxProps,
  type WindowsPhoneRadioProps,
  type WindowsPhoneToggleProps,
  type WindowsPhoneDropdownProps,
} from './WindowsPhoneControls';

// Windows Phone Profile
export {
  WindowsPhoneProfile,
  WindowsPhoneProfileSmall,
  WindowsPhoneProfileMedium,
  WindowsPhoneProfileLarge,
  WindowsPhoneProfileHero,
  WindowsPhoneProfileAccent,
  WindowsPhoneProfileDark,
  type WindowsPhoneProfileProps,
  type WindowsPhoneProfileSize,
  type WindowsPhoneProfileVariant,
} from './WindowsPhoneProfile';

// Windows Phone Layout
export {
  WindowsPhoneLayout,
  WindowsPhoneDefaultLayout,
  WindowsPhoneAccentLayout,
  WindowsPhoneDarkLayout,
  WindowsPhoneFullscreenLayout,
  type WindowsPhoneLayoutProps,
  type WindowsPhoneLayoutVariant,
} from '../layout/WindowsPhoneLayout';

// Windows Phone Navigation
export {
  WindowsPhoneBottomNav,
  WindowsPhoneTopNav,
  WindowsPhoneTabNav,
  type WindowsPhoneBottomNavProps,
  type WindowsPhoneTopNavProps,
  type WindowsPhoneTabNavProps,
} from '../navigation/WindowsPhoneNavigation';

// Re-exportar componente existente
export { IconSymbol } from './IconSymbol';
