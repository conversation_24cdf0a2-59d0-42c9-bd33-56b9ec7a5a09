import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { 
  FluentPrimaryButton, 
  FluentSecondaryButton, 
  FluentOutlineButton,
  FluentTextInput,
  FluentPasswordInput,
  WindowsPhonePrimaryCard,
  WindowsPhoneAccentCard,
  WindowsPhoneNeutralCard,
  WindowsPhoneSuccessCard,
} from './ui';
import { FluentHome, FluentPerson, FluentSettings, FluentLock } from './FluentIconComponent';
import { WindowsPhoneColors } from '../constants/WindowsPhoneColors';
import { SpacingScale } from '../constants/Spacing';

const WindowsPhoneComponentsDemo = () => {
  const [textValue, setTextValue] = useState('');
  const [passwordValue, setPasswordValue] = useState('');
  const [emailValue, setEmailValue] = useState('');

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>
        Componentes Windows Phone UI
      </Text>

      {/* Estado de implementación */}
      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>
          ✅ Componentes Básicos Mejorados
        </Text>
        <Text style={styles.infoText}>
          Componentes rediseñados con estilo Windows Phone UI:
        </Text>
        <Text style={styles.infoText}>
          • Botones minimalistas con colores vibrantes
        </Text>
        <Text style={styles.infoText}>
          • Inputs limpios con estados de foco
        </Text>
        <Text style={styles.infoText}>
          • Cards estilo tiles de Windows Phone
        </Text>
        <Text style={styles.infoText}>
          • Sistema de espaciado consistente
        </Text>
        <Text style={styles.infoText}>
          • Tipografía IBM Plex Sans optimizada
        </Text>
      </View>

      {/* Botones */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Botones Windows Phone
        </Text>
        
        <View style={styles.buttonContainer}>
          <FluentPrimaryButton
            title="Primary"
            onPress={() => console.log('Primary pressed')}
            size="medium"
            icon={<FluentHome size={20} color="white" />}
          />
          
          <FluentSecondaryButton
            title="Secondary"
            onPress={() => console.log('Secondary pressed')}
            size="medium"
            icon={<FluentPerson size={20} color={WindowsPhoneColors.light.text.primary} />}
          />
          
          <FluentOutlineButton
            title="Outline"
            onPress={() => console.log('Outline pressed')}
            size="medium"
            icon={<FluentSettings size={20} color={WindowsPhoneColors.accent.blue} />}
          />
        </View>

        {/* Botones de diferentes tamaños */}
        <View style={styles.sizeContainer}>
          <FluentPrimaryButton
            title="Small"
            onPress={() => {}}
            size="small"
          />
          
          <FluentPrimaryButton
            title="Medium"
            onPress={() => {}}
            size="medium"
          />
          
          <FluentPrimaryButton
            title="Large"
            onPress={() => {}}
            size="large"
          />
        </View>

        {/* Botón con loading */}
        <View style={styles.loadingContainer}>
          <FluentPrimaryButton
            title="Loading..."
            onPress={() => {}}
            loading={true}
            size="medium"
          />
        </View>
      </View>

      {/* Inputs */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Inputs Windows Phone
        </Text>
        
        <View style={styles.inputContainer}>
          <FluentTextInput
            label="Texto Normal"
            placeholder="Escribe algo aquí..."
            value={textValue}
            onChangeText={setTextValue}
            leftIcon={<FluentPerson size={20} color={WindowsPhoneColors.light.text.tertiary} />}
            helperText="Este es un input de texto normal"
          />
          
          <FluentPasswordInput
            label="Contraseña"
            placeholder="Tu contraseña"
            value={passwordValue}
            onChangeText={setPasswordValue}
            leftIcon={<FluentLock size={20} color={WindowsPhoneColors.light.text.tertiary} />}
            required
          />
          
          <FluentTextInput
            label="Email con Error"
            placeholder="<EMAIL>"
            value={emailValue}
            onChangeText={setEmailValue}
            variant="underlined"
            error={emailValue && !emailValue.includes('@') ? 'Email inválido' : undefined}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <FluentTextInput
            label="Input Deshabilitado"
            placeholder="No puedes escribir aquí"
            value="Valor fijo"
            onChangeText={() => {}}
            disabled={true}
            variant="filled"
          />
        </View>
      </View>

      {/* Cards */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Cards Windows Phone
        </Text>
        
        <View style={styles.cardGrid}>
          <WindowsPhonePrimaryCard
            title="Inicio"
            subtitle="Dashboard"
            icon={<FluentHome />}
            size="medium"
            onPress={() => console.log('Home card pressed')}
          />
          
          <WindowsPhoneAccentCard
            title="Perfil"
            subtitle="Usuario"
            icon={<FluentPerson />}
            size="medium"
            onPress={() => console.log('Profile card pressed')}
          />
          
          <WindowsPhoneSuccessCard
            title="Config"
            subtitle="Ajustes"
            icon={<FluentSettings />}
            size="medium"
            onPress={() => console.log('Settings card pressed')}
          />
          
          <WindowsPhoneNeutralCard
            title="Más"
            subtitle="Opciones"
            size="medium"
            onPress={() => console.log('More card pressed')}
          />
        </View>

        {/* Card ancha */}
        <View style={styles.wideCardContainer}>
          <WindowsPhonePrimaryCard
            title="Card Ancha"
            subtitle="Perfecta para contenido horizontal"
            icon={<FluentHome />}
            size="wide"
            onPress={() => console.log('Wide card pressed')}
          />
        </View>
      </View>

      {/* Variantes de colores */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Variantes de Color
        </Text>
        
        <View style={styles.colorVariants}>
          <FluentPrimaryButton
            title="Primary"
            onPress={() => {}}
            variant="primary"
            size="small"
          />
          
          <FluentSecondaryButton
            title="Secondary"
            onPress={() => {}}
            variant="secondary"
            size="small"
          />
          
          <FluentOutlineButton
            title="Outline"
            onPress={() => {}}
            variant="outline"
            size="small"
          />
        </View>
      </View>

      {/* Información técnica */}
      <View style={styles.techSection}>
        <Text style={styles.techTitle}>
          🔧 Características Técnicas
        </Text>
        <Text style={styles.techText}>
          • Colores Windows Phone UI (#0078D4, #00BCF2, etc.)
        </Text>
        <Text style={styles.techText}>
          • Espaciado consistente (8px, 16px, 24px, 32px)
        </Text>
        <Text style={styles.techText}>
          • Tipografía IBM Plex Sans Light/Medium
        </Text>
        <Text style={styles.techText}>
          • Esquinas sutiles (4px border radius)
        </Text>
        <Text style={styles.techText}>
          • Estados interactivos (hover, focus, disabled)
        </Text>
        <Text style={styles.techText}>
          • Soporte para iconos Fluent UI
        </Text>
        <Text style={styles.techText}>
          • Optimizado para performance con StyleSheet
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: WindowsPhoneColors.light.background.primary,
    padding: SpacingScale.lg,
  },
  title: {
    fontSize: 24,
    fontFamily: 'IBMPlexSans-Bold',
    textAlign: 'center',
    marginBottom: SpacingScale.xl,
    color: WindowsPhoneColors.light.text.primary,
  },
  section: {
    marginBottom: SpacingScale.xl,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'IBMPlexSans-SemiBold',
    marginBottom: SpacingScale.lg,
    color: WindowsPhoneColors.light.text.primary,
  },
  infoSection: {
    backgroundColor: WindowsPhoneColors.light.background.tertiary,
    padding: SpacingScale.lg,
    borderRadius: 8,
    marginBottom: SpacingScale.xl,
    borderWidth: 1,
    borderColor: WindowsPhoneColors.light.border.primary,
  },
  infoTitle: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: WindowsPhoneColors.accent.blue,
    marginBottom: SpacingScale.sm,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.secondary,
    marginBottom: 4,
  },
  buttonContainer: {
    gap: SpacingScale.md,
    marginBottom: SpacingScale.lg,
  },
  sizeContainer: {
    flexDirection: 'row',
    gap: SpacingScale.sm,
    marginBottom: SpacingScale.lg,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  inputContainer: {
    gap: SpacingScale.lg,
  },
  cardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SpacingScale.md,
    marginBottom: SpacingScale.lg,
  },
  wideCardContainer: {
    marginBottom: SpacingScale.lg,
  },
  colorVariants: {
    flexDirection: 'row',
    gap: SpacingScale.sm,
    flexWrap: 'wrap',
  },
  techSection: {
    backgroundColor: WindowsPhoneColors.light.background.secondary,
    padding: SpacingScale.lg,
    borderRadius: 8,
    marginBottom: SpacingScale.xl,
  },
  techTitle: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-SemiBold',
    color: WindowsPhoneColors.light.text.primary,
    marginBottom: SpacingScale.sm,
  },
  techText: {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.secondary,
    marginBottom: 4,
  },
});

export default WindowsPhoneComponentsDemo;
