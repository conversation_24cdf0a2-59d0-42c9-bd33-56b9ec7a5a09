import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { useRouter, usePathname } from 'expo-router';
import { FluentHome, FluentPerson, FluentSettings } from '../FluentIconComponent';

// Definir las rutas de navegación
const navItems = [
  {
    name: 'Inicio',
    route: '/(tabs)/',
    icon: FluentHome,
    activeRoutes: ['/', '/(tabs)/', '/(tabs)/index'],
  },
  {
    name: 'Perfil',
    route: '/(tabs)/profile',
    icon: FluentPerson,
    activeRoutes: ['/profile', '/(tabs)/profile'],
  },
  {
    name: 'Config',
    route: '/(tabs)/explore',
    icon: FluentSettings,
    activeRoutes: ['/explore', '/(tabs)/explore'],
  },
];

export const Windows10NavBar: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();

  // Función para determinar si una ruta está activa
  const isActiveRoute = (activeRoutes: string[]) => {
    return activeRoutes.some(route => 
      pathname === route || 
      pathname.endsWith(route) ||
      (route === '/' && pathname === '/(tabs)')
    );
  };

  // Función para navegar
  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  return (
    <View style={styles.container}>
      {/* Línea superior muy sutil */}
      <View style={styles.topLine} />
      
      {/* Contenedor de navegación */}
      <View style={styles.navContainer}>
        {navItems.map((item, index) => {
          const isActive = isActiveRoute(item.activeRoutes);
          const IconComponent = item.icon;
          
          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.navItem,
                isActive && styles.navItemActive,
              ]}
              onPress={() => handleNavigation(item.route)}
              activeOpacity={0.8}
            >
              {/* Icono */}
              <View style={styles.iconContainer}>
                <IconComponent
                  size={20}
                  color={isActive ? '#0078D4' : '#757575'}
                />
              </View>
              
              {/* Texto */}
              <Text style={[
                styles.navText,
                isActive && styles.navTextActive,
              ]}>
                {item.name}
              </Text>
              
              {/* Indicador inferior minimalista */}
              {isActive && <View style={styles.bottomIndicator} />}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 8,
  },
  topLine: {
    height: 0.5,
    backgroundColor: '#E5E5E5',
  },
  navContainer: {
    flexDirection: 'row',
    height: 56,
    paddingHorizontal: 0,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    position: 'relative',
    backgroundColor: 'transparent',
  },
  navItemActive: {
    backgroundColor: 'rgba(0, 120, 212, 0.04)',
  },
  iconContainer: {
    marginBottom: 4,
  },
  navText: {
    fontSize: 10,
    fontFamily: 'IBMPlexSans-Light',
    color: '#757575',
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  navTextActive: {
    color: '#0078D4',
    fontFamily: 'IBMPlexSans-Regular',
  },
  bottomIndicator: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    marginLeft: -16,
    width: 32,
    height: 2,
    backgroundColor: '#0078D4',
  },
});

export default Windows10NavBar;
