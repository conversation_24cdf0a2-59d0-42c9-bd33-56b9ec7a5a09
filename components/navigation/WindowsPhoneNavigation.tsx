import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { WindowsPhoneColors, getThemeColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale } from '../../constants/Spacing';

const { width: screenWidth } = Dimensions.get('window');

// Props para la navegación inferior
export interface WindowsPhoneBottomNavProps {
  onBackPress?: () => void;
  onHomePress?: () => void;
  onSearchPress?: () => void;
  showBack?: boolean;
  showHome?: boolean;
  showSearch?: boolean;
  isDark?: boolean;
  style?: any;
}

export const WindowsPhoneBottomNav: React.FC<WindowsPhoneBottomNavProps> = ({
  onBackPress,
  onHomePress,
  onSearchPress,
  showBack = true,
  showHome = true,
  showSearch = true,
  isDark = false,
  style,
}) => {
  const theme = getThemeColors(isDark);
  
  const navStyle = {
    flexDirection: 'row' as const,
    height: 60,
    backgroundColor: theme.background.primary,
    borderTopWidth: 1,
    borderTopColor: theme.border.primary,
    alignItems: 'center' as const,
    justifyContent: 'space-around' as const,
    paddingHorizontal: SpacingScale.lg,
  };

  const buttonStyle = {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'transparent',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  const iconStyle = {
    fontSize: 24,
    color: theme.text.primary,
  };

  return (
    <View style={[navStyle, style]}>
      {/* Back Button */}
      {showBack && (
        <TouchableOpacity
          style={buttonStyle}
          onPress={onBackPress}
          activeOpacity={0.7}
        >
          <View style={styles.backIcon}>
            <View style={[styles.backArrow, { backgroundColor: theme.text.primary }]} />
          </View>
        </TouchableOpacity>
      )}
      
      {/* Home Button */}
      {showHome && (
        <TouchableOpacity
          style={buttonStyle}
          onPress={onHomePress}
          activeOpacity={0.7}
        >
          <View style={styles.homeIcon}>
            <View style={[styles.homeSquare, { borderColor: theme.text.primary }]} />
          </View>
        </TouchableOpacity>
      )}
      
      {/* Search Button */}
      {showSearch && (
        <TouchableOpacity
          style={buttonStyle}
          onPress={onSearchPress}
          activeOpacity={0.7}
        >
          <View style={styles.searchIcon}>
            <View style={[styles.searchCircle, { borderColor: theme.text.primary }]} />
            <View style={[styles.searchHandle, { backgroundColor: theme.text.primary }]} />
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

// Navegación superior con título
export interface WindowsPhoneTopNavProps {
  title?: string;
  subtitle?: string;
  leftAction?: React.ReactNode;
  rightAction?: React.ReactNode;
  backgroundColor?: string;
  isDark?: boolean;
  style?: any;
}

export const WindowsPhoneTopNav: React.FC<WindowsPhoneTopNavProps> = ({
  title,
  subtitle,
  leftAction,
  rightAction,
  backgroundColor,
  isDark = false,
  style,
}) => {
  const theme = getThemeColors(isDark);
  
  const navStyle = {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: SpacingScale.lg,
    paddingVertical: SpacingScale.md,
    backgroundColor: backgroundColor || theme.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: theme.border.primary,
    minHeight: 60,
  };

  const titleContainerStyle = {
    flex: 1,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  const titleStyle = {
    fontSize: 18,
    fontFamily: 'IBMPlexSans-Medium',
    color: theme.text.primary,
    textAlign: 'center' as const,
  };

  const subtitleStyle = {
    fontSize: 14,
    fontFamily: 'IBMPlexSans-Light',
    color: theme.text.secondary,
    textAlign: 'center' as const,
    marginTop: 2,
  };

  const actionContainerStyle = {
    width: 48,
    height: 48,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  return (
    <View style={[navStyle, style]}>
      {/* Left Action */}
      <View style={actionContainerStyle}>
        {leftAction}
      </View>
      
      {/* Title */}
      <View style={titleContainerStyle}>
        {title && (
          <View>
            <Text style={titleStyle}>
              {title}
            </Text>
            {subtitle && (
              <Text style={subtitleStyle}>
                {subtitle}
              </Text>
            )}
          </View>
        )}
      </View>
      
      {/* Right Action */}
      <View style={actionContainerStyle}>
        {rightAction}
      </View>
    </View>
  );
};

// Navegación de pestañas estilo Windows Phone
export interface WindowsPhoneTabNavProps {
  tabs: Array<{
    key: string;
    title: string;
    icon?: React.ReactNode;
  }>;
  activeTab: string;
  onTabPress: (key: string) => void;
  isDark?: boolean;
  style?: any;
}

export const WindowsPhoneTabNav: React.FC<WindowsPhoneTabNavProps> = ({
  tabs,
  activeTab,
  onTabPress,
  isDark = false,
  style,
}) => {
  const theme = getThemeColors(isDark);
  
  const tabNavStyle = {
    flexDirection: 'row' as const,
    backgroundColor: theme.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: theme.border.primary,
  };

  const getTabStyle = (isActive: boolean) => ({
    flex: 1,
    paddingVertical: SpacingScale.md,
    paddingHorizontal: SpacingScale.sm,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderBottomWidth: isActive ? 3 : 0,
    borderBottomColor: isActive ? WindowsPhoneColors.accent.blue : 'transparent',
  });

  const getTabTextStyle = (isActive: boolean) => ({
    fontSize: 14,
    fontFamily: isActive ? 'IBMPlexSans-Medium' : 'IBMPlexSans-Light',
    color: isActive ? WindowsPhoneColors.accent.blue : theme.text.secondary,
    textAlign: 'center' as const,
    marginTop: 4,
  });

  return (
    <View style={[tabNavStyle, style]}>
      {tabs.map((tab) => {
        const isActive = tab.key === activeTab;
        
        return (
          <TouchableOpacity
            key={tab.key}
            style={getTabStyle(isActive)}
            onPress={() => onTabPress(tab.key)}
            activeOpacity={0.7}
          >
            {tab.icon && (
              <View style={{ marginBottom: 4 }}>
                {React.cloneElement(tab.icon as React.ReactElement, {
                  color: isActive ? WindowsPhoneColors.accent.blue : theme.text.secondary,
                  size: 20,
                })}
              </View>
            )}
            <Text style={getTabTextStyle(isActive)}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  // Back arrow icon
  backIcon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backArrow: {
    width: 12,
    height: 2,
    transform: [{ rotate: '45deg' }],
  },
  
  // Home icon (Windows logo)
  homeIcon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  homeSquare: {
    width: 20,
    height: 20,
    borderWidth: 2,
  },
  
  // Search icon
  searchIcon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    position: 'absolute',
    top: 2,
    left: 2,
  },
  searchHandle: {
    width: 6,
    height: 2,
    position: 'absolute',
    bottom: 2,
    right: 2,
    transform: [{ rotate: '45deg' }],
  },
});

// Exportaciones individuales para compatibilidad
export {
  WindowsPhoneBottomNav,
  WindowsPhoneTopNav,
  WindowsPhoneTabNav,
  type WindowsPhoneBottomNavProps,
  type WindowsPhoneTopNavProps,
  type WindowsPhoneTabNavProps,
};

export default {
  WindowsPhoneBottomNav,
  WindowsPhoneTopNav,
  WindowsPhoneTabNav,
};
