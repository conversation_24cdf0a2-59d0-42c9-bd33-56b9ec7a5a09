import React, { useState } from 'react';
import { <PERSON>, ScrollView, StyleSheet } from 'react-native';
import { 
  WindowsPhoneAccentLayout,
  WindowsPhoneBottomNav,
  WindowsPhoneTopNav,
  WindowsPhoneProfile,
  WindowsPhonePrimaryCard,
  WindowsPhoneAccentCard,
  WindowsPhoneNeutralCard,
  FluentPrimaryButton,
  FluentSecondaryButton,
  FluentTextInput,
  WindowsPhoneToggle,
} from '../ui';
import { FluentArrowLeft, FluentHome, FluentSearch, FluentPerson, FluentMail, FluentSettings, FluentPhone } from '../FluentIconComponent';
import { WindowsPhoneColors } from '../../constants/WindowsPhoneColors';
import { SpacingScale } from '../../constants/Spacing';

const WindowsPhoneScreenExample = () => {
  const [searchValue, setSearchValue] = useState('');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  return (
    <View style={styles.container}>
      {/* Layout principal con navegación superior */}
      <WindowsPhoneAccentLayout
        title="AMY COOK"
        style={styles.mainLayout}
        headerContent={
          <WindowsPhoneTopNav
            leftAction={
              <FluentArrowLeft 
                size={20} 
                color={WindowsPhoneColors.light.text.onAccent} 
              />
            }
            rightAction={
              <FluentSearch 
                size={20} 
                color={WindowsPhoneColors.light.text.onAccent} 
              />
            }
            backgroundColor="transparent"
            isDark={true}
          />
        }
        footerContent={
          <WindowsPhoneBottomNav
            onBackPress={() => console.log('Back')}
            onHomePress={() => console.log('Home')}
            onSearchPress={() => console.log('Search')}
            isDark={true}
          />
        }
      >
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Perfil principal */}
          <View style={styles.profileSection}>
            <WindowsPhoneProfile
              name="Amy Cook"
              subtitle="Unlocked an achievement in Minecraft for 40"
              description="Adventuring Time: Discover 17 of the 23 different biomes"
              size="hero"
              variant="accent"
              centerContent={true}
              showStatus={true}
              statusText="Xbox about an hour ago"
            />
          </View>

          {/* Acciones rápidas */}
          <View style={styles.actionsSection}>
            <View style={styles.actionRow}>
              <FluentPrimaryButton
                title="Message"
                onPress={() => console.log('Message')}
                icon={<FluentMail size={20} color="white" />}
                size="medium"
                style={styles.actionButton}
              />
              <FluentSecondaryButton
                title="Call mobile"
                onPress={() => console.log('Call')}
                icon={<FluentPhone size={20} color={WindowsPhoneColors.light.text.primary} />}
                size="medium"
                style={styles.actionButton}
              />
            </View>
            
            <View style={styles.actionRow}>
              <FluentPrimaryButton
                title="Video call"
                onPress={() => console.log('Video call')}
                variant="outline"
                size="medium"
                style={styles.actionButton}
              />
              <FluentSecondaryButton
                title="Email work"
                onPress={() => console.log('Email')}
                variant="subtle"
                size="medium"
                style={styles.actionButton}
              />
            </View>
          </View>

          {/* Cards de información */}
          <View style={styles.cardsSection}>
            <View style={styles.cardRow}>
              <WindowsPhonePrimaryCard
                title="Messages"
                subtitle="3 new"
                icon={<FluentMail />}
                size="medium"
                onPress={() => console.log('Messages')}
              />
              
              <WindowsPhoneAccentCard
                title="Calls"
                subtitle="2 missed"
                icon={<FluentPhone />}
                size="medium"
                onPress={() => console.log('Calls')}
              />
            </View>
            
            <View style={styles.cardRow}>
              <WindowsPhoneNeutralCard
                title="Contacts"
                subtitle="245 total"
                icon={<FluentPerson />}
                size="medium"
                onPress={() => console.log('Contacts')}
              />
              
              <WindowsPhoneNeutralCard
                title="Settings"
                subtitle="Configure"
                icon={<FluentSettings />}
                size="medium"
                onPress={() => console.log('Settings')}
              />
            </View>
          </View>

          {/* Búsqueda */}
          <View style={styles.searchSection}>
            <FluentTextInput
              placeholder="Search contacts..."
              value={searchValue}
              onChangeText={setSearchValue}
              leftIcon={<FluentSearch size={20} color={WindowsPhoneColors.light.text.tertiary} />}
              variant="filled"
            />
          </View>

          {/* Configuraciones */}
          <View style={styles.settingsSection}>
            <WindowsPhoneToggle
              label="Push notifications"
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
            />
            
            <WindowsPhoneToggle
              label="Auto-sync contacts"
              value={true}
              onValueChange={() => {}}
            />
            
            <WindowsPhoneToggle
              label="Show online status"
              value={false}
              onValueChange={() => {}}
            />
          </View>

          {/* Contactos recientes */}
          <View style={styles.contactsSection}>
            <View style={styles.contactItem}>
              <WindowsPhoneProfile
                name="John Smith"
                subtitle="Mobile: +****************"
                description="Last seen 2 hours ago"
                size="medium"
              />
            </View>
            
            <View style={styles.contactItem}>
              <WindowsPhoneProfile
                name="Sarah Johnson"
                subtitle="Work: +****************"
                description="Available"
                size="medium"
              />
            </View>
            
            <View style={styles.contactItem}>
              <WindowsPhoneProfile
                name="Mike Wilson"
                subtitle="Mobile: +****************"
                description="Busy"
                size="medium"
              />
            </View>
          </View>
        </ScrollView>
      </WindowsPhoneAccentLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: WindowsPhoneColors.accent.blue,
  },
  mainLayout: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 0,
  },
  profileSection: {
    paddingVertical: SpacingScale.xl,
    alignItems: 'center',
  },
  actionsSection: {
    paddingHorizontal: SpacingScale.lg,
    marginBottom: SpacingScale.xl,
  },
  actionRow: {
    flexDirection: 'row',
    gap: SpacingScale.md,
    marginBottom: SpacingScale.md,
  },
  actionButton: {
    flex: 1,
  },
  cardsSection: {
    paddingHorizontal: SpacingScale.lg,
    marginBottom: SpacingScale.xl,
  },
  cardRow: {
    flexDirection: 'row',
    gap: SpacingScale.md,
    marginBottom: SpacingScale.md,
  },
  searchSection: {
    paddingHorizontal: SpacingScale.lg,
    marginBottom: SpacingScale.xl,
  },
  settingsSection: {
    paddingHorizontal: SpacingScale.lg,
    marginBottom: SpacingScale.xl,
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: SpacingScale.lg,
  },
  contactsSection: {
    paddingHorizontal: SpacingScale.lg,
    marginBottom: SpacingScale.xl,
  },
  contactItem: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingHorizontal: SpacingScale.md,
    paddingVertical: SpacingScale.sm,
    marginBottom: SpacingScale.sm,
    borderRadius: 4,
  },
});

export default WindowsPhoneScreenExample;
