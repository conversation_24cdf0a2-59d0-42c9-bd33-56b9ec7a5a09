import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { 
  FluentPrimaryButton,
  WindowsPhoneLayout,
  WindowsPhoneProfile,
} from './ui';
import { FluentHome } from './FluentIconComponent';
import { WindowsPhoneColors } from '../constants/WindowsPhoneColors';
import { SpacingScale } from '../constants/Spacing';

const WindowsPhoneTestDemo = () => {
  return (
    <WindowsPhoneLayout
      title="Test Demo"
      subtitle="Verificando importaciones"
      style={styles.container}
    >
      <View style={styles.content}>
        <Text style={styles.text}>
          ✅ Todas las importaciones funcionan correctamente
        </Text>
        
        <WindowsPhoneProfile
          name="Test User"
          subtitle="Test subtitle"
          size="medium"
        />
        
        <FluentPrimaryButton
          title="Test Button"
          onPress={() => console.log('Test')}
          icon={<FluentHome size={20} color="white" />}
        />
      </View>
    </WindowsPhoneLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: SpacingScale.lg,
  },
  text: {
    fontSize: 16,
    fontFamily: 'IBMPlexSans-Light',
    color: WindowsPhoneColors.light.text.primary,
    textAlign: 'center',
  },
});

export default WindowsPhoneTestDemo;
