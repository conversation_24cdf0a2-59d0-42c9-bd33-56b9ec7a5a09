import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { FluentPrimaryButton } from './ui';
import { buildUrl, getAuthHeaders } from '../config/api';

const TestConnectionButton = () => {
  const { token, user } = useAuth();
  const [testing, setTesting] = useState(false);

  const testConnection = async () => {
    setTesting(true);
    
    try {
      console.log('🧪 === INICIANDO PRUEBAS DE CONEXIÓN ===');
      console.log('👤 Usuario:', user);
      console.log('🔑 Token disponible:', !!token);
      console.log('🔐 Roles:', user?.roles);
      
      // Prueba 1: Endpoint público (si existe)
      console.log('\n🧪 Prueba 1: Endpoint de session-info');
      const sessionUrl = buildUrl('/api/auth/session-info');
      console.log('📡 URL:', sessionUrl);
      
      const sessionResponse = await fetch(sessionUrl, {
        method: 'GET',
        headers: getAuthHeaders(token),
        mode: 'cors',
      });
      
      console.log('📊 Session Response Status:', sessionResponse.status);
      console.log('📊 Session Response Headers:', Object.fromEntries(sessionResponse.headers.entries()));
      
      if (sessionResponse.ok) {
        const sessionData = await sessionResponse.json();
        console.log('✅ Session data:', sessionData);
      } else {
        console.log('❌ Session error:', await sessionResponse.text());
      }
      
      // Prueba 2: Endpoint de admin
      console.log('\n🧪 Prueba 2: Endpoint de vendedores');
      const vendorsUrl = buildUrl('/api/admin/usuarios/vendedores');
      console.log('📡 URL:', vendorsUrl);
      
      const vendorsResponse = await fetch(vendorsUrl, {
        method: 'GET',
        headers: getAuthHeaders(token),
        mode: 'cors',
      });
      
      console.log('📊 Vendors Response Status:', vendorsResponse.status);
      console.log('📊 Vendors Response Headers:', Object.fromEntries(vendorsResponse.headers.entries()));
      
      if (vendorsResponse.ok) {
        const vendorsData = await vendorsResponse.json();
        console.log('✅ Vendors data:', vendorsData);
        Alert.alert('Éxito', `Conexión exitosa. ${vendorsData.length} vendedores encontrados.`);
      } else {
        const errorText = await vendorsResponse.text();
        console.log('❌ Vendors error:', errorText);
        Alert.alert('Error', `Status: ${vendorsResponse.status}\nError: ${errorText}`);
      }
      
    } catch (error: any) {
      console.error('🚨 Error en prueba de conexión:', error);
      
      if (error.message?.includes('CORS')) {
        Alert.alert('Error CORS', 'Problema de CORS detectado. El backend necesita configuración CORS.');
      } else if (error.message?.includes('NetworkError') || error.message?.includes('Failed to fetch')) {
        Alert.alert('Error de Red', 'No se puede conectar al servidor. ¿Está ejecutándose en puerto 8080?');
      } else {
        Alert.alert('Error', error.message || 'Error desconocido');
      }
    } finally {
      setTesting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 Diagnóstico de Conexión</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>Usuario: {user?.username || 'No logueado'}</Text>
        <Text style={styles.infoText}>Roles: {user?.roles?.join(', ') || 'Sin roles'}</Text>
        <Text style={styles.infoText}>Token: {token ? '✅ Presente' : '❌ Ausente'}</Text>
      </View>
      
      <FluentPrimaryButton
        title={testing ? "Probando..." : "Probar Conexión"}
        onPress={testConnection}
        disabled={testing || !token}
        style={styles.button}
      />
      
      <Text style={styles.note}>
        Esta prueba verificará la conectividad con el backend y mostrará información detallada en la consola.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  button: {
    marginBottom: 12,
  },
  note: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default TestConnectionButton;
