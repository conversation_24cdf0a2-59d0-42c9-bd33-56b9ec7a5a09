import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import {
  // Iconos comunes de Fluent UI
  HomeFilled,
  HomeRegular,
  HeartFilled,
  HeartRegular,
  PersonFilled,
  PersonRegular,
  SettingsFilled,
  SettingsRegular,
  SearchFilled,
  SearchRegular,
  ShoppingCartFilled,
  ShoppingCartRegular,
  StarFilled,
  StarRegular,
  MailFilled,
  MailRegular,
  CalendarFilled,
  CalendarRegular,
  DocumentFilled,
  DocumentRegular,
} from '@fluentui/react-icons';

// Componente wrapper para hacer los iconos compatibles con React Native
const IconWrapper = ({ children, color = '#000', size = 24 }: {
  children: React.ReactNode;
  color?: string;
  size?: number;
}) => {
  return (
    <View style={{ width: size, height: size }}>
      {React.cloneElement(children as React.ReactElement, {
        style: {
          fontSize: size,
          color: color,
          width: size,
          height: size,
        }
      })}
    </View>
  );
};

const FluentIconExample = () => {
  return (
    <ScrollView className="flex-1 bg-white p-4">
      <Text className="text-2xl font-bold text-center mb-6 text-gray-800">
        Fluent UI Icons Demo
      </Text>
      
      {/* Sección de iconos básicos */}
      <View className="mb-8">
        <Text className="text-lg font-semibold mb-4 text-gray-700">
          Iconos Básicos (Filled & Regular)
        </Text>
        
        <View className="flex-row flex-wrap justify-around">
          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#3B82F6" size={32}>
              <HomeFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Home Filled</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#3B82F6" size={32}>
              <HomeRegular />
            </IconWrapper>
            <Text className="text-xs mt-1">Home Regular</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#EF4444" size={32}>
              <HeartFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Heart Filled</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#EF4444" size={32}>
              <HeartRegular />
            </IconWrapper>
            <Text className="text-xs mt-1">Heart Regular</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Sección de iconos de navegación */}
      <View className="mb-8">
        <Text className="text-lg font-semibold mb-4 text-gray-700">
          Iconos de Navegación
        </Text>
        
        <View className="flex-row flex-wrap justify-around">
          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#10B981" size={32}>
              <PersonFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Person</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#6B7280" size={32}>
              <SettingsFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Settings</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#8B5CF6" size={32}>
              <SearchFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Search</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#F97316" size={32}>
              <ShoppingCartFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Cart</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Sección de iconos de aplicación */}
      <View className="mb-8">
        <Text className="text-lg font-semibold mb-4 text-gray-700">
          Iconos de Aplicación
        </Text>
        
        <View className="flex-row flex-wrap justify-around">
          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#EAB308" size={32}>
              <StarFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Star</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#2563EB" size={32}>
              <MailFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Mail</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#6366F1" size={32}>
              <CalendarFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Calendar</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center m-2">
            <IconWrapper color="#374151" size={32}>
              <DocumentFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">Document</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Sección de diferentes tamaños */}
      <View className="mb-8">
        <Text className="text-lg font-semibold mb-4 text-gray-700">
          Diferentes Tamaños
        </Text>
        
        <View className="flex-row items-center justify-around">
          <TouchableOpacity className="items-center">
            <IconWrapper color="#EF4444" size={16}>
              <HeartFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">16px</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center">
            <IconWrapper color="#EF4444" size={24}>
              <HeartFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">24px</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center">
            <IconWrapper color="#EF4444" size={32}>
              <HeartFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">32px</Text>
          </TouchableOpacity>

          <TouchableOpacity className="items-center">
            <IconWrapper color="#EF4444" size={48}>
              <HeartFilled />
            </IconWrapper>
            <Text className="text-xs mt-1">48px</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

export default FluentIconExample;
